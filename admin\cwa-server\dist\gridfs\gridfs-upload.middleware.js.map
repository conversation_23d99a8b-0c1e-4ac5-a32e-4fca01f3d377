{"version": 3, "file": "gridfs-upload.middleware.js", "sourceRoot": "", "sources": ["../../src/gridfs/gridfs-upload.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,iCAAiC;AACjC,iEAAsD;AACtD,6BAA6B;AAC7B,iCAAiC;AACjC,2CAA+C;AAGxC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGjC,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QAEtB,MAAM,OAAO,GAAG,IAAI,qCAAa,CAAC;YAChC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,aAAa,CAAC;YAClD,OAAO,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE;YAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAErC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAClC,IAAI,GAAG,EAAE,CAAC;4BACR,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;wBACrB,CAAC;wBAED,MAAM,QAAQ,GAAG;4BACf,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;4BACzG,UAAU,EAAE,SAAS;4BACrB,QAAQ,EAAE;gCACR,YAAY,EAAE,IAAI,CAAC,YAAY;gCAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,UAAU,EAAE,IAAI,IAAI,EAAE;6BACvB;yBACF,CAAC;wBAEF,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;YAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;QAGF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACnB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE;gBACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;aAC1B;SACF,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAEjD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAC7C,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;YAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,EAAE;SAC1C,CAAC,CAAC;QAEH,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE;YACtC,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,oBAAoB;oBAC7B,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;YACL,CAAC;YACD,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,CAAC,gBAAgB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,sCAAsC,CAAC;QAEnF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,qCAAa,CAAC;YAChC,GAAG,EAAE,QAAQ;YACb,OAAO,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE;YAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAClC,IAAI,GAAG,EAAE,CAAC;4BACR,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;wBACrB,CAAC;wBAED,MAAM,QAAQ,GAAG;4BACf,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;4BACzG,UAAU,EAAE,SAAS;4BACrB,QAAQ,EAAE;gCACR,YAAY,EAAE,IAAI,CAAC,YAAY;gCAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,UAAU,EAAE,IAAI,IAAI,EAAE;6BACvB;yBACF,CAAC;wBAEF,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;YAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE;gBACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;aAC1B;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA9HY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAIwB,sBAAa;GAHrC,sBAAsB,CA8HlC"}