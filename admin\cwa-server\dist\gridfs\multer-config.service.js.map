{"version": 3, "file": "multer-config.service.js", "sourceRoot": "", "sources": ["../../src/gridfs/multer-config.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,iEAAsD;AACtD,6BAA6B;AAC7B,iCAAiC;AAG1B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAEpD,mBAAmB;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,aAAa,CAAC,CAAC;QAE/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,IAAI,qCAAa,CAAC;YACvB,GAAG,EAAE,QAAQ;YACb,OAAO,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE;YAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAClC,IAAI,GAAG,EAAE,CAAC;4BACR,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;wBACrB,CAAC;wBAED,MAAM,QAAQ,GAAG;4BACf,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;4BACzG,UAAU,EAAE,SAAS;4BACrB,QAAQ,EAAE;gCACR,YAAY,EAAE,IAAI,CAAC,YAAY;gCAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,UAAU,EAAE,IAAI,IAAI,EAAE;6BACvB;yBACF,CAAC;wBAEF,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3C,MAAM,UAAU,GAAG,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;YAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE;gBACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;aAC1B;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAxDY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAEwB,sBAAa;GADrC,mBAAmB,CAwD/B"}