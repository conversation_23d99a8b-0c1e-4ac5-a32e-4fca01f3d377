import { <PERSON>, Controller, Post, UseGuards, <PERSON>q, HttpStatus, HttpException } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";

import { ForgotPasswordDto } from "./dto/forget-password.dto";
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from "./dto/change-password.dto";
import { VerifyOtpDTO } from "./dto/verify-otp.dto";
import { AuthGuard } from "./auth.guard";

@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post("register")
    async signUp(@Body() signUpDto:CreateUserDto):Promise<{
        status: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
            token: string;
        };
    }>{
        try {
            const result = await this.authService.signUp(signUpDto);
            return {
                status: "success",
                data: {
                    user: {
                        id: result._id,
                        name: result.name,
                        email: result.email,
                        phone: result.phone,
                    },
                    token: result.accessToken,
                },
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'Registration failed',
                },
                error.status || HttpStatus.BAD_REQUEST,
            );
        }
    }

    @Post("login")
    async signIn(@Body() signInDto:signInDto):Promise<{
        status: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
            token: string;
        };
    }>{
        try {
            const result = await this.authService.signIn(signInDto);
            return {
                status: "success",
                data: {
                    user: {
                        id: result._id,
                        name: result.name,
                        email: result.email,
                        phone: result.phone,
                    },
                    token: result.accessToken,
                },
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'Login failed',
                },
                error.status || HttpStatus.UNAUTHORIZED,
            );
        }
    }

    @Post('forgot-password')
    async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto):Promise<{
        status: string;
        message: string;
    }> {
        try {
            const result = await this.authService.forgotPassword(forgotPasswordDto);
            return {
                status: "success",
                message: result.message,
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'Failed to send password reset email',
                },
                error.status || HttpStatus.BAD_REQUEST,
            );
        }
    }

    @Post('verify-otp')
    async verifyOtp(@Body() verifyOtpDto: VerifyOtpDTO):Promise<{
        status: string;
        message: string;
        resetToken?: string;
    }> {
        try {
            const result = await this.authService.verifyOtp(verifyOtpDto);
            return {
                status: "success",
                message: "OTP verified successfully",
                resetToken: result.resetToken,
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'OTP verification failed',
                },
                error.status || HttpStatus.BAD_REQUEST,
            );
        }
    }

    @Post('reset-password')
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto):Promise<{
        status: string;
        message: string;
    }> {
        try {
            await this.authService.resetPassword(resetPasswordDto);
            return {
                status: "success",
                message: "Password reset successful",
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'Password reset failed',
                },
                error.status || HttpStatus.BAD_REQUEST,
            );
        }
    }

    @Post('change-password')
    @UseGuards(AuthGuard)
    async changePassword(@Req() req: any, @Body() changePasswordDto: ChangePasswordDto):Promise<{
        status: string;
        message: string;
    }> {
        try {
            const userId = req.user._id;
            const result = await this.authService.changePassword(userId, changePasswordDto);
            return {
                status: "success",
                message: result.message,
            };
        } catch (error) {
            throw new HttpException(
                {
                    status: "fail",
                    message: error.message || 'Password change failed',
                },
                error.status || HttpStatus.BAD_REQUEST,
            );
        }
    }
}