import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

@Schema({ timestamps: true })
export class Product extends mongoose.Document {
  @Prop({
    required: true,
    maxlength: [100, 'Product name cannot exceed 100 characters'],
  })
  name: string;

  @Prop({
    required: true,
    enum: ['regular', 'featured', 'sale', 'new'],
    default: 'regular'
  })
  type: string;

  @Prop({
    required: true,
    min: [0, 'Price cannot be negative'],
  })
  price: number;

  @Prop({
    required: true,
  })
  image: string;

  @Prop({
    required: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
  })
  description: string;

  @Prop({
    required: true,
    min: [0, 'Quantity cannot be negative'],
  })
  quantity: number;

  @Prop({
    type: Boolean,
    default: true,
  })
  stock: boolean;

  @Prop({
    required: true,
  })
  category: string;

  @Prop({
    type: [String],
    default: [],
  })
  images: string[];

  @Prop({
    required: false,
  })
  video: string;

  @Prop({
    required: false,
  })
  discount: string;

  @Prop({
    required: false,
  })
  id: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
