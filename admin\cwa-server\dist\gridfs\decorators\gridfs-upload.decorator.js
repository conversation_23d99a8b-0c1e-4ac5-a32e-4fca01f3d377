"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GridFSFileUpload = GridFSFileUpload;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_gridfs_storage_1 = require("multer-gridfs-storage");
const path = require("path");
const crypto = require("crypto");
function GridFSFileUpload() {
    const createStorage = () => {
        const mongoUri = process.env.MONGODB_URI;
        if (!mongoUri) {
            throw new Error('MONGODB_URI environment variable is not set');
        }
        return new multer_gridfs_storage_1.GridFsStorage({
            url: mongoUri,
            options: { useNewUrlParser: true, useUnifiedTopology: true },
            file: (req, file) => {
                return new Promise((resolve, reject) => {
                    crypto.randomBytes(16, (err, buf) => {
                        if (err) {
                            return reject(err);
                        }
                        const fileInfo = {
                            filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
                            bucketName: 'uploads',
                            metadata: {
                                originalname: file.originalname,
                                mimetype: file.mimetype,
                                uploadDate: new Date()
                            }
                        };
                        resolve(fileInfo);
                    });
                });
            }
        });
    };
    const storage = createStorage();
    const fileFilter = (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        }
        else {
            cb(new Error('Not an image! Please upload only images.'), false);
        }
    };
    const multerOptions = {
        storage: storage,
        fileFilter: fileFilter,
        limits: {
            fileSize: 5 * 1024 * 1024
        }
    };
    return (0, common_1.UseInterceptors)((0, platform_express_1.FileFieldsInterceptor)([
        { name: 'image', maxCount: 1 },
        { name: 'additionalImages', maxCount: 5 },
    ], multerOptions));
}
//# sourceMappingURL=gridfs-upload.decorator.js.map