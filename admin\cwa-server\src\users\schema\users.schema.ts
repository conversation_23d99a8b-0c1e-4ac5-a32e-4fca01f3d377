import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

@Schema({ timestamps: true })
export class Users extends mongoose.Document {
  @Prop({
    required: true,
    maxlength: [25, 'Name cannot exceed 25 characters'],
    match: [/^[A-Za-z\s]+$/, 'Name can only contain alphabets and spaces'],
  })
  name: string;

  @Prop({
    required: true,
    lowercase: true,
    match: [/\S+@\S+\.\S+/, 'Please enter a valid email'],
    unique: true,
  })
  email: string;

  @Prop({
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false, // Don't include password in queries by default
  })
  password: string;

  @Prop({
    required: [true, 'Phone number is required'],
    match: [/^\d{11}$/, 'Phone number must be exactly 11 digits'],
  })
  phone: string;

  @Prop({
    type: Boolean,
    default: false,
  })
  isAdmin: boolean;

  @Prop({
    type: String,
    default: null,
  })
  resetPasswordToken: string;

  @Prop({
    type: String,
    default: null,
  })
  resetPasswordOTP: string;

  @Prop({
    type: Date,
    default: null,
  })
  resetPasswordExpires: Date;

  // firstName and lastName for compatibility with existing auth service
  @Prop({
    required: false,
  })
  firstName: string;

  @Prop({
    required: false,
  })
  lastName: string;
}

export const UsersSchema = SchemaFactory.createForClass(Users);

