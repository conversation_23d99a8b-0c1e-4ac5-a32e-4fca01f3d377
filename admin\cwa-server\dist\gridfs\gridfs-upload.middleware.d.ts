import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
export declare class GridFSUploadMiddleware implements NestMiddleware {
    private configService;
    private upload;
    constructor(configService: ConfigService);
    private initializeUpload;
    use(req: Request, res: Response, next: NextFunction): void;
    static getMulterOptions(): {
        storage: import("multer-gridfs-storage/lib/gridfs").GridFsStorage;
        fileFilter: (req: any, file: any, cb: any) => void;
        limits: {
            fileSize: number;
        };
    };
}
