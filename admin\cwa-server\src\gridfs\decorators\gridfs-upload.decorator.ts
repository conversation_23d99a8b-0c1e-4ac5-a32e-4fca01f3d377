import { UseInterceptors } from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { GridFsStorage } from 'multer-gridfs-storage';
import * as path from 'path';
import * as crypto from 'crypto';

// Create a function that returns the decorator with proper GridFS configuration
export function GridFSFileUpload() {
  // Create a lazy-loaded storage factory
  const createStorage = () => {
    // Get MongoDB URI from environment variables
    const mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }

    return new GridFsStorage({
      url: mongoUri,
      options: { useNewUrlParser: true, useUnifiedTopology: true },
      file: (req, file) => {
        return new Promise((resolve, reject) => {
          crypto.randomBytes(16, (err, buf) => {
            if (err) {
              return reject(err);
            }

            const fileInfo = {
              filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
              bucketName: 'uploads',
              metadata: {
                originalname: file.originalname,
                mimetype: file.mimetype,
                uploadDate: new Date()
              }
            };

            resolve(fileInfo);
          });
        });
      }
    });
  };

  const storage = createStorage();

  const fileFilter = (req: any, file: any, cb: any) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Not an image! Please upload only images.'), false);
    }
  };

  const multerOptions = {
    storage: storage,
    fileFilter: fileFilter,
    limits: {
      fileSize: 5 * 1024 * 1024 // 5MB limit
    }
  };

  return UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'additionalImages', maxCount: 5 },
    ], multerOptions)
  );
}
