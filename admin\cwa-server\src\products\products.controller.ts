import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { AdminGuard } from '../auth/admin.guard';
import { IProductResponse, IProductQuery } from './interfaces/product.interface';

@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  // Public routes
  @Get()
  async getAllProducts(@Query() query: IProductQuery): Promise<IProductResponse> {
    try {
      const result = await this.productsService.findAll(query);
      return {
        status: 'success',
        results: result.results,
        data: {
          products: result.products,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Error fetching products',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getProduct(@Param('id') id: string): Promise<IProductResponse> {
    try {
      const product = await this.productsService.findById(id);
      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Error fetching product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Admin-only routes
  @Post()
  @UseGuards(AdminGuard)
  async createProduct(@Body() createProductDto: CreateProductDto): Promise<IProductResponse> {
    try {
      const product = await this.productsService.createProduct(createProductDto);
      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to create product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  async updateProduct(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
  ): Promise<IProductResponse> {
    try {
      const product = await this.productsService.updateProduct(id, updateProductDto);
      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to update product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  async deleteProduct(@Param('id') id: string): Promise<{ status: string; message: string }> {
    try {
      await this.productsService.deleteProduct(id);
      return {
        status: 'success',
        message: 'Product deleted successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to delete product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
