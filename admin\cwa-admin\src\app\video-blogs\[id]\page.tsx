'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import VideoPlayer from '@/components/video-blogs/VideoPlayer';
import videoBlogService from '@/lib/api/videoBlogService';
import { VideoBlog } from '@/types/user';
import { toast } from 'sonner';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  CalendarIcon, 
  EyeIcon, 
  TagIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

export default function VideoBlogDetailPage() {
  const router = useRouter();
  const params = useParams();
  const [videoBlog, setVideoBlog] = useState<VideoBlog | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const videoBlogId = params.id as string;

  useEffect(() => {
    const fetchVideoBlog = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await videoBlogService.getVideoBlog(videoBlogId);
        
        if (response.status === 'success') {
          setVideoBlog(response.data.videoBlog);
        } else {
          throw new Error('Failed to fetch video blog');
        }
      } catch (error: unknown) {
        console.error('Error fetching video blog:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch video blog');
        toast.error('Failed to load video blog');
      } finally {
        setLoading(false);
      }
    };

    if (videoBlogId) {
      fetchVideoBlog();
    }
  }, [videoBlogId]);

  const handleBack = () => {
    router.push('/video-blogs');
  };

  const handleEdit = () => {
    router.push(`/video-blogs/${videoBlogId}/edit`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getVideoType = (videoBlog: VideoBlog) => {
    if (videoBlog.youtubeVideoId || videoBlog.youtubeUrl) {
      return 'YouTube';
    }
    return 'Direct Video';
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="aspect-video bg-gray-200 rounded-lg mb-6"></div>
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !videoBlog) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-center">
                <div className="text-red-500 text-lg font-medium mb-2">
                  {error || 'Video blog not found'}
                </div>
                <p className="text-gray-600 mb-4">
                  The video blog you&apos;re looking for doesn&apos;t exist or couldn&apos;t be loaded.
                </p>
                <button
                  onClick={handleBack}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Video Blogs
                </button>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        {/* Responsive container: Mobile/Tablet: full width with padding, Desktop: increased max-width */}
        <div className="w-full max-w-none lg:max-w-7xl xl:max-w-[1400px] 2xl:max-w-[1600px] mx-auto space-y-4 md:space-y-6 p-3 sm:p-4 md:p-6 lg:p-8">
          {/* Header with Navigation - Mobile optimized */}
          <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-3 sm:gap-4 lg:flex-row lg:items-center lg:justify-between">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                <button
                  onClick={handleBack}
                  className="inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors w-full sm:w-auto"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to List
                </button>
                <div className="min-w-0 flex-1">
                  <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 break-words leading-tight">
                    {videoBlog.title}
                  </h1>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleEdit}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors w-full sm:w-auto"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </button>
              </div>
            </div>
          </div>

          {/* Video Player Section - Responsive aspect ratio */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="aspect-video">
              <VideoPlayer
                videoBlog={videoBlog}
                className="w-full h-full"
              />
            </div>
          </div>

          {/* Video Information - Responsive grid layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-4 md:space-y-6">
              {/* Description */}
              <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Description</h2>
                <div className="prose max-w-none">
                  <p className="text-gray-700 whitespace-pre-wrap leading-relaxed text-sm sm:text-base">
                    {videoBlog.description}
                  </p>
                </div>
              </div>

              {/* Tags */}
              {videoBlog.tags && videoBlog.tags.length > 0 && (
                <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
                  <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                    <TagIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Tags
                  </h2>
                  <div className="flex flex-wrap gap-2">
                    {videoBlog.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar - Mobile: full width, Desktop: sidebar */}
            <div className="space-y-4 md:space-y-6">
              {/* Video Details */}
              <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Video Details</h2>
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-center text-xs sm:text-sm text-gray-600">
                    <CalendarIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Published {formatDate(videoBlog.createdAt)}</span>
                  </div>

                  <div className="flex items-center text-xs sm:text-sm text-gray-600">
                    <EyeIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{videoBlog.views.toLocaleString()} views</span>
                  </div>

                  {videoBlog.duration && (
                    <div className="flex items-center text-xs sm:text-sm text-gray-600">
                      <PlayIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span>{formatDuration(videoBlog.duration)} duration</span>
                    </div>
                  )}

                  <div className="pt-2 border-t border-gray-200">
                    <div className="text-xs sm:text-sm text-gray-600">
                      <span className="font-medium">Category:</span> {videoBlog.category}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 mt-1">
                      <span className="font-medium">Type:</span> {getVideoType(videoBlog)}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 mt-1">
                      <span className="font-medium">Status:</span>
                      <span className={`ml-1 ${videoBlog.isActive ? 'text-green-600' : 'text-red-600'}`}>
                        {videoBlog.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
