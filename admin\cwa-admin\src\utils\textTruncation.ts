/**
 * Utility functions for text truncation
 */

/**
 * Truncate text to a specific number of words
 * @param text - The text to truncate
 * @param wordCount - Number of words to keep
 * @returns Truncated text with ellipsis if needed
 */
export function truncateToWords(text: string, wordCount: number): string {
  if (!text) return '';
  const words = text.trim().split(/\s+/);
  if (words.length <= wordCount) return text;
  return words.slice(0, wordCount).join(' ') + '...';
}

/**
 * Truncate text to a specific character length
 * @param text - The text to truncate
 * @param maxLength - Maximum character length
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}


// Export for use in components
export { truncateToWords as default };
