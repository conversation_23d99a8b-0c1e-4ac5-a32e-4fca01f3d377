0000000000000000000000000000000000000000 572be274770582ed9734926c9a75136a416d9fc2 Anas-<PERSON>-3673 <<EMAIL>> 1750990051 +0500	commit (initial): first commit
572be274770582ed9734926c9a75136a416d9fc2 0000000000000000000000000000000000000000 Anas-Ali-3673 <<EMAIL>> 1750990052 +0500	Branch: renamed refs/heads/master to refs/heads/main
0000000000000000000000000000000000000000 572be274770582ed9734926c9a75136a416d9fc2 Anas-Ali-3673 <<EMAIL>> 1750990052 +0500	Branch: renamed refs/heads/master to refs/heads/main
572be274770582ed9734926c9a75136a416d9fc2 9933866833b463bc772b6c724577097643f19e1c Anas-Ali-3673 <<EMAIL>> 1750990092 +0500	commit: first commit
9933866833b463bc772b6c724577097643f19e1c 47bc61de950b118309701cff0f41ade9729e8c3f Anas-Ali-3673 <<EMAIL>> 1750991789 +0500	commit: Refactor API routes and services to handle DELETE requests with improved error handling and authorization
47bc61de950b118309701cff0f41ade9729e8c3f 8e4eb0a6126e205149197f40004168f003b4ca59 Anas-Ali-3673 <<EMAIL>> 1750993030 +0500	commit: Enhance mobile responsiveness and UI consistency across login, dashboard, and layout components
8e4eb0a6126e205149197f40004168f003b4ca59 b2ac94daef4fcb13b76dcf34fade0afeb8a63f62 Anas-Ali-3673 <<EMAIL>> 1751942257 +0500	commit: add product
b2ac94daef4fcb13b76dcf34fade0afeb8a63f62 df12c2eb10850a2e60f804f8c34ca1464aa52980 Anas-Ali-3673 <<EMAIL>> 1752041097 +0500	commit: blogs
df12c2eb10850a2e60f804f8c34ca1464aa52980 26133e1451b8b92fd87f91143d0933ca8670a2a9 Anas-Ali-3673 <<EMAIL>> 1752557526 +0500	commit: fixes
26133e1451b8b92fd87f91143d0933ca8670a2a9 574d2999051b49eba26ce23e60fe95435917f9c5 Anas-Ali-3673 <<EMAIL>> 1752902189 +0500	commit: baisc bugs
574d2999051b49eba26ce23e60fe95435917f9c5 0000000000000000000000000000000000000000 Anas-Ali-3673 <<EMAIL>> 1752903231 +0500	Branch: renamed refs/heads/main to refs/heads/main
574d2999051b49eba26ce23e60fe95435917f9c5 574d2999051b49eba26ce23e60fe95435917f9c5 Anas-Ali-3673 <<EMAIL>> 1752903231 +0500	Branch: renamed refs/heads/main to refs/heads/main
574d2999051b49eba26ce23e60fe95435917f9c5 0000000000000000000000000000000000000000 Anas-Ali-3673 <<EMAIL>> 1752903304 +0500	Branch: renamed refs/heads/main to refs/heads/master
0000000000000000000000000000000000000000 574d2999051b49eba26ce23e60fe95435917f9c5 Anas-Ali-3673 <<EMAIL>> 1752903304 +0500	Branch: renamed refs/heads/main to refs/heads/master
574d2999051b49eba26ce23e60fe95435917f9c5 8bc1557455701f7a908ec26899d66415f9df5c47 Anas-Ali-3673 <<EMAIL>> 1752910496 +0500	commit: feedbck
8bc1557455701f7a908ec26899d66415f9df5c47 524bb941551d0b257fdd5106e698844d5219552e Anas-Ali-3673 <<EMAIL>> 1752912239 +0500	commit: fix dashboard
524bb941551d0b257fdd5106e698844d5219552e dbfa71cc2dc12d357da288d2477b512a0365265a Anas-Ali-3673 <<EMAIL>> 1752917924 +0500	commit: fic detail page
dbfa71cc2dc12d357da288d2477b512a0365265a 64ad794db3182f298b3b0b1ebaacb714d38ab150 Anas-Ali-3673 <<EMAIL>> 1752926671 +0500	commit: final products
64ad794db3182f298b3b0b1ebaacb714d38ab150 da33aab1d9a99272ba28702caf6d87f0c69aba89 Anas-Ali-3673 <<EMAIL>> 1752928651 +0500	commit: fix details
da33aab1d9a99272ba28702caf6d87f0c69aba89 82d65dc3c3783621cfe4fab06fce06ab49782701 Anas-Ali-3673 <<EMAIL>> 1752937797 +0500	commit: functional blogs
82d65dc3c3783621cfe4fab06fce06ab49782701 79ef488eab67bf0eabf59fd08ea04cc5ca1e503b Anas-Ali-3673 <<EMAIL>> 1752938961 +0500	commit: fix blogs table
79ef488eab67bf0eabf59fd08ea04cc5ca1e503b a1dd6259ef85aa67bfdc55faa17908076f959131 Anas-Ali-3673 <<EMAIL>> 1752943024 +0500	commit: orders
a1dd6259ef85aa67bfdc55faa17908076f959131 f0d766dd58b0f9dd56ef74cad4456ce3e58c665e Anas-Ali-3673 <<EMAIL>> 1752988516 +0500	commit: orders
f0d766dd58b0f9dd56ef74cad4456ce3e58c665e 8b7003fe4fda92cfa422ac4d8db68316657d0f5a Anas-Ali-3673 <<EMAIL>> 1753451730 +0500	commit: update .gitignore and tsconfig for new build directory; remove unused settings icon from Sidebar
8b7003fe4fda92cfa422ac4d8db68316657d0f5a 7290f73df1fa5fa43f546d46837282d6ddde36ca Anas-Ali-3673 <<EMAIL>> 1753709354 +0500	commit: refactor: streamline image handling and proxy functionality; remove unused build directory setting
