"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/products/view/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/products/view/[id]/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CalendarIcon.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api_productService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/productService */ \"(app-pages-browser)/./src/lib/api/productService.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/imageUtils */ \"(app-pages-browser)/./src/lib/utils/imageUtils.ts\");\n/* harmony import */ var _components_ui_ImageGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ImageGallery */ \"(app-pages-browser)/./src/components/ui/ImageGallery.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ProductDetailPage() {\n    var _product__id;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const productId = params.id;\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch product data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            const fetchProduct = {\n                \"ProductDetailPage.useEffect.fetchProduct\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await _lib_api_productService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getProduct(productId);\n                        if (response.status === 'success') {\n                            setProduct(response.data);\n                        } else {\n                            throw new Error('Failed to fetch product');\n                        }\n                    } catch (err) {\n                        console.error('Error fetching product:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to fetch product');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductDetailPage.useEffect.fetchProduct\"];\n            if (productId) {\n                fetchProduct();\n            }\n        }\n    }[\"ProductDetailPage.useEffect\"], [\n        productId\n    ]);\n    // Get product type configuration with functional icons\n    const getTypeConfig = (type)=>{\n        switch(type){\n            case 'featured':\n                return {\n                    label: 'Featured Product',\n                    className: 'bg-purple-50 text-purple-700 border-purple-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 17\n                    }, this)\n                };\n            case 'sale':\n                return {\n                    label: 'On Sale',\n                    className: 'bg-red-50 text-red-700 border-red-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 17\n                    }, this)\n                };\n            case 'new':\n                return {\n                    label: 'New Arrival',\n                    className: 'bg-green-50 text-green-700 border-green-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 17\n                    }, this)\n                };\n            default:\n                return {\n                    label: 'Regular Product',\n                    className: 'bg-gray-50 text-gray-700 border-gray-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 17\n                    }, this)\n                };\n        }\n    };\n    // Get stock status configuration with functional icons\n    const getStockConfig = (stock)=>{\n        return stock ? {\n            label: 'In Stock',\n            className: 'bg-green-50 text-green-700 border-green-200',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 17\n            }, this)\n        } : {\n            label: 'Out of Stock',\n            className: 'bg-red-50 text-red-700 border-red-200',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this)\n        };\n    };\n    // Handle back navigation\n    const handleBack = ()=>{\n        router.back();\n    };\n    // Handle edit navigation\n    const handleEdit = ()=>{\n        router.push(\"/products/edit/\".concat(productId));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gray-200 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-200 rounded w-48\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-200 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 mt-4\",\n                                                    children: [\n                                                        ...Array(4)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gray-200 rounded-lg\"\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"Error Loading Product\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error || 'Product not found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBack,\n                                className: \"bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors\",\n                                children: \"Go Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare images for gallery\n    const allImages = (0,_lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__.getImageUrls)([\n        product.image,\n        ...product.images || []\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleBack,\n                                        className: \"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors\",\n                                        \"aria-label\": \"Go back to products list\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Back to Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300 hidden sm:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleEdit,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Edit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Edit Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xl:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden sticky top-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ImageGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        images: allImages,\n                                        alt: product.name,\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight mb-2\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Product ID: \",\n                                                                ((_product__id = product._id) === null || _product__id === void 0 ? void 0 : _product__id.slice(-8).toUpperCase()) || 'N/A'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    role: \"group\",\n                                                    \"aria-label\": \"Product status indicators\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border \".concat(getTypeConfig(product.type).className),\n                                                            role: \"status\",\n                                                            \"aria-label\": \"Product type: \".concat(getTypeConfig(product.type).label),\n                                                            children: [\n                                                                getTypeConfig(product.type).icon,\n                                                                getTypeConfig(product.type).label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border \".concat(getStockConfig(product.stock).className),\n                                                            role: \"status\",\n                                                            \"aria-label\": \"Stock status: \".concat(getStockConfig(product.stock).label),\n                                                            children: [\n                                                                getStockConfig(product.stock).icon,\n                                                                getStockConfig(product.stock).label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border bg-orange-50 text-orange-700 border-orange-200\",\n                                                            role: \"status\",\n                                                            \"aria-label\": \"Discount: \".concat(product.discount, \"% off\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                product.discount,\n                                                                \"% OFF\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border-l-4 border-l-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Pricing Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-baseline gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-4xl font-bold text-gray-900\",\n                                                                        \"aria-label\": \"Current price: \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)),\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl text-gray-500 line-through\",\n                                                                        \"aria-label\": \"Original price: \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100))),\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 rounded-lg p-3 border border-green-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-700 font-medium flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"You save \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100) - product.price),\n                                                                        \" (\",\n                                                                        product.discount,\n                                                                        \"% off)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.quantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 bg-gray-50 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price per unit:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Product Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Available Quantity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: [\n                                                                    product.quantity,\n                                                                    \" units\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Added\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(product.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEdit,\n                                                    className: \"w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                    children: \"Edit Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                            \"aria-label\": \"Add to favorites\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"hidden lg:inline\",\n                                                                    children: \"Favorite\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                            \"aria-label\": \"Share product\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"hidden lg:inline\",\n                                                                    children: \"Share\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    product.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 lg:p-8 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Product Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed whitespace-pre-wrap\",\n                                        children: product.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailPage, \"Q2HgUvuAVEf9J7HA/Ehsq101e8Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/view/[id]/page.tsx\n"));

/***/ })

});