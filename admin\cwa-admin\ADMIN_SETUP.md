# CWA Admin Dashboard Setup

## Overview
This is the admin dashboard for Chinioti Wooden Art (CWA) e-commerce platform. It provides admin users with the ability to manage users, products, orders, and system settings.

## Features Implemented

### ✅ Authentication
- Admin login with email/password
- JWT token-based authentication
- Admin role verification
- Automatic logout functionality
- Protected routes

### ✅ User Management
- View all users in a table format
- Display user fields: name, email, phone, role, created date
- Search functionality
- Delete users
- Real-time data fetching from backend

### ✅ Navigation & Layout
- Responsive sidebar with navigation
- Dashboard, Users, Products, Orders, Settings pages
- User profile display in header
- Active route highlighting

### ✅ Dashboard
- Welcome message with user name
- Statistics cards (Users, Products, Orders, Revenue)
- Recent activity table

## Setup Instructions

### 1. Environment Configuration
Create a `.env.local` file in the root directory:
```env
BACKEND_URL=http://localhost:5002
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start the Development Server
```bash
npm run dev
```

### 4. Backend Requirements
Make sure the backend server is running on `http://localhost:5002` with the following endpoints:
- `POST /api/auth/login` - Admin login
- `GET /api/users` - Get all users (requires admin token)
- `DELETE /api/users/:id` - Delete user (requires admin token)

### 5. Admin User Setup
You need to create an admin user in your backend database with `isAdmin: true` field.

## API Integration

### Authentication Flow
1. User enters email/password on login page
2. Frontend sends POST request to `/api/auth/login`
3. Next.js API route proxies request to backend
4. Backend validates credentials and returns JWT token
5. Frontend stores token and user data in localStorage
6. Protected routes check authentication status

### User Management
- Users data is fetched from `/api/users` endpoint
- Table displays all user fields except password and ID
- Real-time search and pagination support
- Delete functionality with confirmation

## File Structure
```
src/
├── app/
│   ├── api/                 # Next.js API routes (proxy to backend)
│   ├── auth/login/          # Login page
│   ├── dashboard/           # Dashboard page
│   ├── users/               # Users management page
│   ├── products/            # Products page (placeholder)
│   ├── orders/              # Orders page (placeholder)
│   └── settings/            # Settings page
├── components/
│   ├── auth/                # Authentication components
│   └── layout/              # Layout components (Sidebar, Header)
├── context/
│   └── AuthContext.tsx      # Authentication context
├── services/
│   └── userService.ts       # User API service
└── types/
    └── user.ts              # TypeScript interfaces
```

## Next Steps
1. Implement product management functionality
2. Add order management features
3. Create settings page for system configuration
4. Add user creation/editing forms
5. Implement real-time notifications
6. Add data export functionality

## Testing
To test the admin dashboard:
1. Ensure backend is running with admin user created
2. Navigate to `http://localhost:3000/auth/login`
3. Login with admin credentials
4. Verify dashboard access and user management features
