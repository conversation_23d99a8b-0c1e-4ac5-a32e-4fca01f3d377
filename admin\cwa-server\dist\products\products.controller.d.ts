import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { IProductResponse, IProductQuery } from './interfaces/product.interface';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    getAllProducts(query: IProductQuery): Promise<IProductResponse>;
    getProduct(id: string): Promise<IProductResponse>;
    createProduct(createProductDto: CreateProductDto): Promise<IProductResponse>;
    updateProduct(id: string, updateProductDto: UpdateProductDto): Promise<IProductResponse>;
    deleteProduct(id: string): Promise<{
        status: string;
        message: string;
    }>;
}
