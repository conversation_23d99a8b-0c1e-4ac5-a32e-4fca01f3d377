/**
 * Utility functions for handling image URLs
 */

/**
 * Get the proper image URL for display
 * @param imageFilename - The filename stored in the database
 * @returns The proper URL to access the image
 */
export function getImageUrl(imageFilename: string | null | undefined): string {
  // Return placeholder if no filename
  if (!imageFilename) {
    return '/placeholder-product.svg';
  }

  // If it's already a full URL, return as is
  if (imageFilename.startsWith('http://') || imageFilename.startsWith('https://')) {
    return imageFilename;
  }

  // If it's a relative path starting with /, return as is
  if (imageFilename.startsWith('/')) {
    return imageFilename;
  }

  // For GridFS filenames, use the image proxy
  return `/api/images/${imageFilename}`;
}

/**
 * Get multiple image URLs
 * @param imageFilenames - Array of filenames
 * @returns Array of proper URLs
 */
export function getImageUrls(imageFilenames: (string | null | undefined)[]): string[] {
  return imageFilenames
    .filter(Boolean)
    .map(filename => getImageUrl(filename));
}

/**
 * Handle image error by setting a placeholder
 * @param event - The error event from img element
 */
export function handleImageError(event: React.SyntheticEvent<HTMLImageElement>) {
  const target = event.target as HTMLImageElement;
  if (target.src !== '/placeholder-product.svg') {
    target.src = '/placeholder-product.svg';
  }
}
