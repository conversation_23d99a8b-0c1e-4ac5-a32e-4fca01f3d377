import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  BadRequestException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import * as multer from 'multer';
import { GridFsStorage } from 'multer-gridfs-storage';
import * as path from 'path';
import * as crypto from 'crypto';

@Injectable()
export class GridFSUploadInterceptor implements NestInterceptor {
  private upload: multer.Multer;

  constructor(private configService: ConfigService) {
    this.initializeUpload();
  }

  private initializeUpload() {
    const mongoUri = this.configService.get<string>('MONGODB_URI');
    
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }

    const storage = new GridFsStorage({
      url: mongoUri,
      options: { useNewUrlParser: true, useUnifiedTopology: true },
      file: (req, file) => {
        return new Promise((resolve, reject) => {
          crypto.randomBytes(16, (err, buf) => {
            if (err) {
              return reject(err);
            }
            
            const fileInfo = {
              filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
              bucketName: 'uploads',
              metadata: {
                originalname: file.originalname,
                mimetype: file.mimetype,
                uploadDate: new Date()
              }
            };
            
            resolve(fileInfo);
          });
        });
      }
    });

    const fileFilter = (req: any, file: any, cb: any) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Not an image! Please upload only images.'), false);
      }
    };

    this.upload = multer({
      storage: storage,
      fileFilter: fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
      }
    });
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    return new Observable((observer) => {
      const uploadFields = this.upload.fields([
        { name: 'image', maxCount: 1 },
        { name: 'additionalImages', maxCount: 5 }
      ]);

      uploadFields(request, response, (error) => {
        if (error) {
          console.error('Upload error:', error);
          observer.error(new BadRequestException({
            status: 'error',
            message: 'File upload failed',
            error: error.message
          }));
        } else {
          // Continue with the request
          next.handle().subscribe({
            next: (data) => observer.next(data),
            error: (err) => observer.error(err),
            complete: () => observer.complete()
          });
        }
      });
    });
  }
}
