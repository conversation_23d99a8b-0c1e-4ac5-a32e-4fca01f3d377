@import "tailwindcss";

:root {
  /* Primary Brand Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  --primary-950: #082f49;

  /* Secondary Colors */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Theme Variables */
  --background: var(--neutral-50);
  --foreground: var(--neutral-900);
  --card-background: #ffffff;
  --border-color: var(--neutral-200);
  --sidebar-background: var(--secondary-800);
  --sidebar-text: #ffffff;
  --header-background: #ffffff;
  --header-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--neutral-900);
    --foreground: var(--neutral-100);
    --card-background: var(--neutral-800);
    --border-color: var(--neutral-700);
    --sidebar-background: var(--neutral-900);
    --header-background: var(--neutral-800);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
}

/* Custom utility classes */
.bg-primary {
  background-color: var(--primary-600);
}

.bg-primary-hover:hover {
  background-color: var(--primary-700);
}

.text-primary {
  color: var(--primary-600);
}

.bg-success {
  background-color: var(--success-600);
}

.bg-success-hover:hover {
  background-color: var(--success-700);
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Responsive table improvements */
@media (max-width: 1024px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Print styles for order details */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

.bg-warning {
  background-color: var(--warning-500);
}

.bg-error {
  background-color: var(--error-600);
}

.bg-error-hover:hover {
  background-color: var(--error-700);
}

.border-primary {
  border-color: var(--primary-600);
}

.shadow-custom {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.shadow-custom-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Enhanced Mobile-specific responsive utilities */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-hidden {
    display: none !important;
  }

  .mobile-block {
    display: block !important;
  }

  .mobile-text-sm {
    font-size: 0.875rem !important;
  }

  .mobile-p-2 {
    padding: 0.5rem !important;
  }

  .mobile-space-y-2 > * + * {
    margin-top: 0.5rem !important;
  }

  /* Mobile card layouts */
  .mobile-card {
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }

  /* Mobile form improvements */
  .mobile-form-spacing {
    margin-bottom: 1rem;
  }

  .mobile-form-input {
    padding: 0.75rem;
    font-size: 1rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  button,
  [role="button"],
  input[type="button"],
  input[type="submit"] {
    min-height: 44px;
  }
}

/* Improved focus states for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid var(--primary-600);
  outline-offset: 2px;
}

/* Responsive table utilities */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .table-responsive table {
    min-width: 600px;
  }
  
  /* Convert tables to cards on mobile */
  .table-mobile-cards {
    display: block;
  }
  
  .table-mobile-cards thead {
    display: none;
  }
  
  .table-mobile-cards tbody {
    display: block;
  }
  
  .table-mobile-cards tr {
    display: block;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }
  
  .table-mobile-cards td {
    display: block;
    border: none;
    padding: 0.5rem 0;
    text-align: left !important;
  }
  
  .table-mobile-cards td:before {
    content: attr(data-label) ": ";
    font-weight: bold;
    display: inline-block;
    width: 100px;
    margin-right: 0.5rem;
  }
}

/* Tablet specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  
  .tablet-grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  
  .tablet-text-sm {
    font-size: 0.875rem !important;
  }
  
  .tablet-p-4 {
    padding: 1rem !important;
  }
}

/* Mobile navigation improvements */
@media (max-width: 1024px) {
  .sidebar-overlay {
    backdrop-filter: blur(4px);
  }
}
