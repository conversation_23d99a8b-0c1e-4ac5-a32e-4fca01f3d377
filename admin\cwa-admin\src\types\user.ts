export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  isAdmin?: boolean;
  createdAt: string;
  updatedAt?: string;
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  setAuthState: (state: AuthState) => void;
}

// Product related types
export interface Product {
  _id: string;
  name: string;
  type: 'regular' | 'featured' | 'sale' | 'new';
  price: number;
  image: string;
  description: string;
  quantity: number;
  stock: boolean;
  category: string;
  images: string[];
  video?: string;
  discount?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductsResponse {
  status: string;
  results: number;
  data: {
    products: Product[];
  };
}

// Video Blog related types
export interface VideoBlog {
  id: string;
  _id?: string;
  title: string;
  description: string;
  videoFileId?: string;
  videoFilename?: string;
  mimetype?: string;
  fileSize?: number;
  duration?: number;
  thumbnailFileId?: string;
  thumbnailFilename?: string;
  videoUrl: string;
  thumbnailUrl: string;
  youtubeUrl?: string;
  youtubeVideoId?: string;
  category: string;
  tags: string[];
  isActive: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
}

export interface VideoBlogsResponse {
  status: string;
  data: {
    videoBlogs: VideoBlog[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
}

export interface VideoBlogResponse {
  status: string;
  data: {
    videoBlog: VideoBlog;
  };
  message?: string;
}

export interface VideoBlogFilters {
  search?: string;
  category?: string;
  tag?: string;
  page?: number;
  limit?: number;
}
