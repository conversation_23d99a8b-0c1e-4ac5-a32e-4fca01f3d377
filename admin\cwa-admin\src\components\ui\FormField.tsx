'use client';

import { ReactNode, forwardRef } from 'react';
import { ExclamationCircleIcon, CheckCircleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

interface FormFieldProps {
  label: string;
  id: string;
  error?: string;
  success?: string;
  hint?: string;
  required?: boolean;
  children: ReactNode;
  className?: string;
}

const FormField = forwardRef<HTMLDivElement, FormFieldProps>(
  ({ label, id, error, success, hint, required, children, className = '' }, ref) => {
    return (
      <div ref={ref} className={`space-y-2 ${className}`}>
        <label 
          htmlFor={id} 
          className="block text-sm font-medium text-gray-700"
        >
          {label}
          {required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
        </label>
        
        <div className="relative">
          {children}
          
          {/* Status icons */}
          {(error || success) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              {error && (
                <ExclamationCircleIcon 
                  className="h-5 w-5 text-red-500" 
                  aria-hidden="true" 
                />
              )}
              {success && !error && (
                <CheckCircleIcon 
                  className="h-5 w-5 text-green-500" 
                  aria-hidden="true" 
                />
              )}
            </div>
          )}
        </div>
        
        {/* Messages */}
        {error && (
          <p className="text-sm text-red-600 flex items-center" role="alert">
            <ExclamationCircleIcon className="h-4 w-4 mr-1 flex-shrink-0" />
            {error}
          </p>
        )}
        
        {success && !error && (
          <p className="text-sm text-green-600 flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-1 flex-shrink-0" />
            {success}
          </p>
        )}
        
        {hint && !error && !success && (
          <p className="text-sm text-gray-500 flex items-center">
            <InformationCircleIcon className="h-4 w-4 mr-1 flex-shrink-0" />
            {hint}
          </p>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export default FormField;

// Input component with enhanced styling
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  success?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className = '', error, success, ...props }, ref) => {
    const baseClasses = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-offset-0 focus:outline-none transition-colors";
    const stateClasses = error 
      ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
      : success
      ? "border-green-300 focus:border-green-500 focus:ring-green-200"
      : "border-gray-300 focus:border-blue-500 focus:ring-blue-200";
    
    return (
      <input
        ref={ref}
        className={`${baseClasses} ${stateClasses} ${className}`}
        {...props}
      />
    );
  }
);

Input.displayName = 'Input';

// Textarea component with enhanced styling
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  success?: boolean;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className = '', error, success, ...props }, ref) => {
    const baseClasses = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-offset-0 focus:outline-none transition-colors resize-vertical";
    const stateClasses = error 
      ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
      : success
      ? "border-green-300 focus:border-green-500 focus:ring-green-200"
      : "border-gray-300 focus:border-blue-500 focus:ring-blue-200";
    
    return (
      <textarea
        ref={ref}
        className={`${baseClasses} ${stateClasses} ${className}`}
        {...props}
      />
    );
  }
);

Textarea.displayName = 'Textarea';

// Select component with enhanced styling
interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  success?: boolean;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ className = '', error, success, children, ...props }, ref) => {
    const baseClasses = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-offset-0 focus:outline-none transition-colors bg-white";
    const stateClasses = error 
      ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
      : success
      ? "border-green-300 focus:border-green-500 focus:ring-green-200"
      : "border-gray-300 focus:border-blue-500 focus:ring-blue-200";
    
    return (
      <select
        ref={ref}
        className={`${baseClasses} ${stateClasses} ${className}`}
        {...props}
      >
        {children}
      </select>
    );
  }
);

Select.displayName = 'Select';

// Checkbox component with enhanced styling
interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: boolean;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className = '', label, error, ...props }, ref) => {
    return (
      <div className="flex items-center">
        <input
          ref={ref}
          type="checkbox"
          className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
            error ? 'border-red-300' : ''
          } ${className}`}
          {...props}
        />
        <label htmlFor={props.id} className="ml-2 block text-sm text-gray-900">
          {label}
        </label>
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';
