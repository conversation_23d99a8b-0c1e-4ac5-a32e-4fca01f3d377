'use client';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  UsersIcon,
  ArrowRightOnRectangleIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  XMarkIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const { logout } = useAuth();
  const pathname = usePathname();

  const handleLogout = () => {
    logout();
  };

  const navItems = [
    { href: '/dashboard', label: 'Dashboard', icon: HomeIcon },
    { href: '/users', label: 'Users', icon: UsersIcon },
    { href: '/products', label: 'Products', icon: ShoppingBagIcon },
    { href: '/video-blogs', label: 'Video Blogs', icon: VideoCameraIcon },
    { href: '/orders', label: 'Orders', icon: ClipboardDocumentListIcon },
  ];

  const handleNavClick = () => {
    // Close sidebar on mobile when navigation item is clicked
    if (window.innerWidth < 1024) {
      onClose();
    }
  };

  return (
    <aside
      className={`
        w-64 h-screen fixed left-0 top-0 z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 bg-slate-800 shadow-xl
      `}
      style={{ backgroundColor: 'var(--sidebar-background)' }}
    >
      <div className="p-4 lg:p-6">
        {/* Mobile Close Button */}
        <div className="flex justify-between items-center mb-6 lg:mb-8 lg:block">
          <div>
            <h1 className="text-xl lg:text-2xl font-bold text-white">CWA Admin</h1>
            <p className="text-xs lg:text-sm text-slate-300 mt-1">Management Portal</p>
          </div>
          <button
            onClick={onClose}
            className="lg:hidden p-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-white transition-colors"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav>
          <ul className="space-y-1 lg:space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    onClick={handleNavClick}
                    className={`flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                    }`}
                  >
                    <item.icon className="w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0" />
                    <span className="font-medium text-sm lg:text-base">{item.label}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Logout Section */}
        <div className="mt-6 lg:mt-8 pt-4 lg:pt-6 border-t border-slate-600">
          <button
            onClick={handleLogout}
            className="flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 w-full text-left"
          >
            <ArrowRightOnRectangleIcon className="w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0" />
            <span className="font-medium text-sm lg:text-base">Logout</span>
          </button>
        </div>
      </div>
    </aside>
  );
}
