import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as multer from 'multer';
import { GridFsStorage } from 'multer-gridfs-storage';
import * as path from 'path';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GridFSUploadMiddleware implements NestMiddleware {
  private upload: multer.Multer;

  constructor(private configService: ConfigService) {
    this.initializeUpload();
  }

  private initializeUpload() {
    // Create GridFS storage engine
    const storage = new GridFsStorage({
      url: this.configService.get<string>('MONGODB_URI'),
      options: { useNewUrlParser: true, useUnifiedTopology: true },
      file: (req, file) => {
        return new Promise((resolve, reject) => {
          // Generate a random 16 character hex string
          crypto.randomBytes(16, (err, buf) => {
            if (err) {
              return reject(err);
            }
            
            const fileInfo = {
              filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
              bucketName: 'uploads',
              metadata: {
                originalname: file.originalname,
                mimetype: file.mimetype,
                uploadDate: new Date()
              }
            };
            
            resolve(fileInfo);
          });
        });
      }
    });

    // File filter to accept only images
    const fileFilter = (req: any, file: any, cb: any) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Not an image! Please upload only images.'), false);
      }
    };

    // Create multer upload instance
    this.upload = multer({
      storage: storage,
      fileFilter: fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
      }
    });
  }

  use(req: Request, res: Response, next: NextFunction) {
    // Middleware for handling product uploads (main image and additional images)
    const uploadProductImages = this.upload.fields([
      { name: 'image', maxCount: 1 },
      { name: 'additionalImages', maxCount: 5 }
    ]);

    uploadProductImages(req, res, (error) => {
      if (error) {
        console.error('Upload error:', error);
        return res.status(400).json({
          status: 'error',
          message: 'File upload failed',
          error: error.message
        });
      }
      next();
    });
  }

  // Static method to get multer storage configuration
  static getMulterOptions() {
    // Get MongoDB URI from environment variables
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/cwa-server';

    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }

    const storage = new GridFsStorage({
      url: mongoUri,
      options: { useNewUrlParser: true, useUnifiedTopology: true },
      file: (req, file) => {
        return new Promise((resolve, reject) => {
          crypto.randomBytes(16, (err, buf) => {
            if (err) {
              return reject(err);
            }

            const fileInfo = {
              filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
              bucketName: 'uploads',
              metadata: {
                originalname: file.originalname,
                mimetype: file.mimetype,
                uploadDate: new Date()
              }
            };

            resolve(fileInfo);
          });
        });
      }
    });

    const fileFilter = (req: any, file: any, cb: any) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Not an image! Please upload only images.'), false);
      }
    };

    return {
      storage: storage,
      fileFilter: fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
      }
    };
  }
}
