/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/page";
exports.ids = ["app/products/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/page\",\n        pathname: \"/products\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQUxJJTIwQ09NUFVURVJTJTVDJTVDRGVza3RvcCU1QyU1Q2FkbWluJTVDJTVDY3dhLWFkbWluJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBTEklMjBDT01QVVRFUlMlNUMlNUNEZXNrdG9wJTVDJTVDYWRtaW4lNUMlNUNjd2EtYWRtaW4lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQUxJJTIwQ09NUFVURVJTJTVDJTVDRGVza3RvcCU1QyU1Q2FkbWluJTVDJTVDY3dhLWFkbWluJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQXNKO0FBQ3RKO0FBQ0EsME9BQXlKO0FBQ3pKO0FBQ0EsME9BQXlKO0FBQ3pKO0FBQ0Esb1JBQStLO0FBQy9LO0FBQ0Esd09BQXdKO0FBQ3hKO0FBQ0EsNFBBQW1LO0FBQ25LO0FBQ0Esa1FBQXNLO0FBQ3RLO0FBQ0Esc1FBQXVLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBTEkgQ09NUFVURVJTXFxcXERlc2t0b3BcXFxcYWRtaW5cXFxcY3dhLWFkbWluXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBTEkgQ09NUFVURVJTXFxcXERlc2t0b3BcXFxcYWRtaW5cXFxcY3dhLWFkbWluXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBTEkgQ09NUFVURVJTXFxcXERlc2t0b3BcXFxcYWRtaW5cXFxcY3dhLWFkbWluXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(rsc)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb2R1Y3RzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3RzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQUxJIENPTVBVVEVSU1xcRGVza3RvcFxcYWRtaW5cXGN3YS1hZG1pblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a96de9275224\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFMSSBDT01QVVRFUlNcXERlc2t0b3BcXGFkbWluXFxjd2EtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE5NmRlOTI3NTIyNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/Providers */ \"(rsc)/./src/components/providers/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"CWA Admin Dashboard\",\n    description: \"Admin dashboard for CWA application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDa0M7QUFZbEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLHVFQUFTQTswQkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCBQcm92aWRlcnMgZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNXQSBBZG1pbiBEYXNoYm9hcmRcIixcbiAgZGVzY3JpcHRpb246IFwiQWRtaW4gZGFzaGJvYXJkIGZvciBDV0EgYXBwbGljYXRpb25cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\app\\products\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\components\\providers\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./lib/api/apiClient.ts":
/*!******************************!*\
  !*** ./lib/api/apiClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(ssr)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(ssr)/./src/app/products/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb2R1Y3RzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3RzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Products)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_products_ProductTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products/ProductTable */ \"(ssr)/./src/components/products/ProductTable.tsx\");\n/* harmony import */ var _components_products_ProductFilters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/products/ProductFilters */ \"(ssr)/./src/components/products/ProductFilters.tsx\");\n/* harmony import */ var _lib_api_productService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/productService */ \"(ssr)/./src/lib/api/productService.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Products() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalProducts, setTotalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Fetch products with filters\n    const fetchProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Products.useCallback[fetchProducts]\": async (filters = {})=>{\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await _lib_api_productService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getProducts(filters);\n                if (response.status === 'success') {\n                    setProducts(response.data.products);\n                    setTotalProducts(response.results);\n                } else {\n                    throw new Error('Failed to fetch products');\n                }\n            } catch (err) {\n                console.error('Error fetching products:', err);\n                setError(err instanceof Error ? err.message : 'Failed to fetch products');\n                setProducts([]);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"Products.useCallback[fetchProducts]\"], []);\n    // Fetch categories\n    const fetchCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Products.useCallback[fetchCategories]\": async ()=>{\n            try {\n                const categoriesList = await _lib_api_productService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getCategories();\n                setCategories(categoriesList);\n            } catch (err) {\n                console.error('Error fetching categories:', err);\n            }\n        }\n    }[\"Products.useCallback[fetchCategories]\"], []);\n    // Delete product\n    const handleDeleteProduct = async (id)=>{\n        try {\n            await _lib_api_productService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].deleteProduct(id);\n            // Update the products list locally without refetching\n            setProducts((prevProducts)=>prevProducts.filter((product)=>product._id !== id));\n            // Show success message\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('Product deleted successfully');\n        } catch (err) {\n            console.error('Error deleting product:', err);\n            setError(err instanceof Error ? err.message : 'Failed to delete product');\n            throw err; // Re-throw to let the component handle it\n        }\n    };\n    // Handle filter changes\n    const handleFiltersChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Products.useCallback[handleFiltersChange]\": (filters)=>{\n            fetchProducts(filters);\n        }\n    }[\"Products.useCallback[handleFiltersChange]\"], [\n        fetchProducts\n    ]);\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Products.useEffect\": ()=>{\n            fetchProducts();\n            fetchCategories();\n        }\n    }[\"Products.useEffect\"], [\n        fetchProducts,\n        fetchCategories\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-4 md:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 md:p-6 rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4 lg:flex-row lg:items-start \",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-w-0 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl md:text-2xl font-bold text-gray-900 break-words\",\n                                            children: \"Products Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1 text-sm md:text-base break-words\",\n                                            children: \"Manage your product catalog with advanced filtering and search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        totalProducts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs md:text-sm text-gray-500 mt-1\",\n                                            children: [\n                                                totalProducts,\n                                                \" product\",\n                                                totalProducts !== 1 ? 's' : '',\n                                                \" total\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-full lg:w-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/products/add'),\n                                        className: \"w-full lg:w-auto bg-blue-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 text-sm md:text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 md:w-5 md:h-5 flex-shrink-0\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"whitespace-nowrap\",\n                                                children: \"Add New Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-full overflow-x-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductFilters__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onFiltersChange: handleFiltersChange,\n                            categories: categories,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-800 break-words\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-full overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_ProductTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            products: products,\n                            loading: loading,\n                            onDelete: handleDeleteProduct\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/products/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children }) {\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, don't render children (redirect will happen)\n    if (!isAuthenticated) {\n        return null;\n    }\n    // If authenticated, render the protected content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50\",\n            style: {\n                backgroundColor: 'var(--background)'\n            },\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onClose: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col lg:ml-64 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onMenuClick: ()=>setSidebarOpen(true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-3 sm:p-4 md:p-6 overflow-y-auto bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-full xl:max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ onMenuClick }) {\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    const getPageTitle = ()=>{\n        switch(pathname){\n            case '/dashboard':\n                return 'Dashboard';\n            case '/users':\n                return 'User Management';\n            case '/products':\n                return 'Product Management';\n            case '/video-blogs':\n                return 'Video Blog Management';\n            case '/video-blogs/add':\n                return 'Add Video Blog';\n            case '/orders':\n                return 'Order Management';\n            case '/settings':\n                return 'Settings';\n            default:\n                if (pathname.startsWith('/video-blogs/') && pathname.includes('/edit')) {\n                    return 'Edit Video Blog';\n                }\n                return 'CWA Admin Dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-14 sm:h-16 flex items-center px-3 sm:px-4 md:px-6 border-b\",\n        style: {\n            backgroundColor: 'var(--header-background)',\n            boxShadow: 'var(--header-shadow)',\n            borderColor: 'var(--border-color)'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMenuClick,\n                className: \"lg:hidden p-1.5 sm:p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-colors mr-2 sm:mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base sm:text-lg md:text-xl font-semibold truncate\",\n                        style: {\n                            color: 'var(--foreground)'\n                        },\n                        children: getPageTitle()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs sm:text-sm text-slate-500 mt-0.5 hidden sm:block\",\n                        children: \"Welcome back to your admin dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-1.5 sm:p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-0.5 sm:top-1 right-0.5 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3 text-slate-700 hover:text-slate-900 focus:outline-none p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm font-medium\",\n                                            children: user?.name || 'Admin User'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 hidden md:block\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm\",\n                                        children: user?.name ? getInitials(user.name) : 'AU'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 hidden sm:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose }) {\n    const { logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: '/users',\n            label: 'Users',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: '/products',\n            label: 'Products',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: '/video-blogs',\n            label: 'Video Blogs',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: '/orders',\n            label: 'Orders',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    const handleNavClick = ()=>{\n        // Close sidebar on mobile when navigation item is clicked\n        if (window.innerWidth < 1024) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `\n        w-64 h-screen fixed left-0 top-0 z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n        lg:translate-x-0 bg-slate-800 shadow-xl\n      `,\n        style: {\n            backgroundColor: 'var(--sidebar-background)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 lg:p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6 lg:mb-8 lg:block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl lg:text-2xl font-bold text-white\",\n                                    children: \"CWA Admin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs lg:text-sm text-slate-300 mt-1\",\n                                    children: \"Management Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"lg:hidden p-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1 lg:space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: handleNavClick,\n                                    className: `flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg transition-all duration-200 ${isActive ? 'bg-blue-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm lg:text-base\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 lg:mt-8 pt-4 lg:pt-6 border-t border-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 w-full text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm lg:text-base\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/products/ProductFilters.tsx":
/*!****************************************************!*\
  !*** ./src/components/products/ProductFilters.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductFilters)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProductFilters({ onFiltersChange, categories, loading }) {\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        category: '',\n        type: '',\n        minPrice: undefined,\n        maxPrice: undefined,\n        inStock: undefined,\n        sort: '-createdAt'\n    });\n    const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductFilters.useEffect\": ()=>{\n            onFiltersChange(filters);\n        }\n    }[\"ProductFilters.useEffect\"], [\n        filters,\n        onFiltersChange\n    ]);\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === '' ? undefined : value\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            category: '',\n            type: '',\n            minPrice: undefined,\n            maxPrice: undefined,\n            inStock: undefined,\n            sort: '-createdAt'\n        });\n    };\n    const hasActiveFilters = Object.values(filters).some((value)=>value !== '' && value !== undefined && value !== '-createdAt');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAdvanced(!showAdvanced),\n                                className: \"text-sm text-blue-600 hover:text-blue-800 transition-colors\",\n                                children: showAdvanced ? 'Hide Advanced' : 'Show Advanced'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearFilters,\n                                className: \"text-sm text-red-600 hover:text-red-800 transition-colors\",\n                                children: \"Clear All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Search Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: filters.search || '',\n                                        onChange: (e)=>handleFilterChange('search', e.target.value),\n                                        placeholder: \"Search by name or description...\",\n                                        className: \"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.category || '',\n                                onChange: (e)=>handleFilterChange('category', e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category,\n                                            children: category\n                                        }, category, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.type || '',\n                                onChange: (e)=>handleFilterChange('type', e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"regular\",\n                                        children: \"Regular\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"featured\",\n                                        children: \"Featured\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"sale\",\n                                        children: \"Sale\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"new\",\n                                        children: \"New\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Sort By\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.sort || '-createdAt',\n                                onChange: (e)=>handleFilterChange('sort', e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-createdAt\",\n                                        children: \"Newest First\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"createdAt\",\n                                        children: \"Oldest First\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"name\",\n                                        children: \"Name A-Z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-name\",\n                                        children: \"Name Z-A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price\",\n                                        children: \"Price Low to High\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-price\",\n                                        children: \"Price High to Low\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-quantity\",\n                                        children: \"Quantity High to Low\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"quantity\",\n                                        children: \"Quantity Low to High\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Stock Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filters.inStock === undefined ? '' : filters.inStock.toString(),\n                                    onChange: (e)=>handleFilterChange('inStock', e.target.value === '' ? undefined : e.target.value === 'true'),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"In Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Out of Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Min Price ($)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: filters.minPrice || '',\n                                    onChange: (e)=>handleFilterChange('minPrice', e.target.value ? parseFloat(e.target.value) : undefined),\n                                    placeholder: \"0\",\n                                    min: \"0\",\n                                    step: \"0.01\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Max Price ($)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: filters.maxPrice || '',\n                                    onChange: (e)=>handleFilterChange('maxPrice', e.target.value ? parseFloat(e.target.value) : undefined),\n                                    placeholder: \"1000\",\n                                    min: \"0\",\n                                    step: \"0.01\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this),\n            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Active filters:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        filters.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                            children: [\n                                \"Search: \",\n                                filters.search,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFilterChange('search', ''),\n                                    className: \"ml-1 text-blue-600 hover:text-blue-800\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 15\n                        }, this),\n                        filters.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                            children: [\n                                \"Category: \",\n                                filters.category,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFilterChange('category', ''),\n                                    className: \"ml-1 text-green-600 hover:text-green-800\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 15\n                        }, this),\n                        filters.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800\",\n                            children: [\n                                \"Type: \",\n                                filters.type,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFilterChange('type', ''),\n                                    className: \"ml-1 text-purple-600 hover:text-purple-800\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 15\n                        }, this),\n                        filters.inStock !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                            children: [\n                                \"Stock: \",\n                                filters.inStock ? 'In Stock' : 'Out of Stock',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFilterChange('inStock', undefined),\n                                    className: \"ml-1 text-yellow-600 hover:text-yellow-800\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductFilters.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/products/ProductFilters.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/products/ProductTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/products/ProductTable.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/imageUtils */ \"(ssr)/./src/lib/utils/imageUtils.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(ssr)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProductTable({ products, loading, onDelete }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isConfirmModalOpen, setIsConfirmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productToDelete, setProductToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleViewProduct = (product)=>{\n        console.log('Viewing product:', product); // Debug log\n        router.push(`/products/view/${product._id}`);\n    };\n    const handleEditProduct = (id)=>{\n        router.push(`/products/edit/${id}`);\n    };\n    const handleDeleteClick = (product)=>{\n        setProductToDelete(product);\n        setIsConfirmModalOpen(true);\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!productToDelete) return;\n        setIsDeleting(true);\n        try {\n            await onDelete(productToDelete._id);\n            setIsConfirmModalOpen(false);\n            setProductToDelete(null);\n        } catch (error) {\n            console.error('Error deleting product:', error);\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleCancelDelete = ()=>{\n        setIsConfirmModalOpen(false);\n        setProductToDelete(null);\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case 'featured':\n                return 'bg-purple-100 text-purple-800';\n            case 'sale':\n                return 'bg-red-100 text-red-800';\n            case 'new':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStockColor = (stock)=>{\n        return stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-gray-200 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    [\n                        ...Array(5)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 bg-gray-100 mb-2\"\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Created\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 h-16 w-16\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    className: \"h-16 w-16 rounded-lg object-cover border border-gray-200\",\n                                                                    src: (0,_lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_4__.getImageUrl)(product.image),\n                                                                    alt: product.name,\n                                                                    onError: _lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_4__.handleImageError\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 max-w-xs truncate\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 138,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 max-w-xs truncate\",\n                                                                        children: product.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: product.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(product.type)}`,\n                                                        children: product.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(product.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-600\",\n                                                            children: [\n                                                                product.discount,\n                                                                \"% off\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockColor(product.stock)}`,\n                                                        children: product.stock ? 'In Stock' : 'Out of Stock'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                    children: product.quantity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(product.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleViewProduct(product),\n                                                                className: \"text-blue-600 hover:text-blue-900 transition-colors\",\n                                                                title: \"View Details\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                            lineNumber: 185,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEditProduct(product._id),\n                                                                className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                title: \"Edit Product\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteClick(product),\n                                                                disabled: isDeleting,\n                                                                className: `transition-colors ${isDeleting ? 'text-red-400 cursor-not-allowed' : 'text-red-600 hover:text-red-900'}`,\n                                                                title: \"Delete Product\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, product._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    products.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCE6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"No products match your current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: isConfirmModalOpen,\n                onClose: handleCancelDelete,\n                onConfirm: handleConfirmDelete,\n                title: \"Delete Product\",\n                message: \"Are you sure you want to delete this product? This action cannot be undone.\",\n                itemName: productToDelete?.name,\n                itemId: productToDelete?._id,\n                confirmText: \"Delete Product\",\n                cancelText: \"Cancel\",\n                isLoading: isDeleting,\n                variant: \"danger\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\products\\\\ProductTable.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/products/ProductTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                richColors: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHcUQ7QUFDcEI7QUFNbEIsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQzVELHFCQUNFLDhEQUFDSCw4REFBWUE7O1lBQ1ZHOzBCQUNELDhEQUFDRiwyQ0FBT0E7Z0JBQUNHLFVBQVM7Z0JBQVlDLFVBQVU7Ozs7Ozs7Ozs7OztBQUc5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdzb25uZXInO1xyXG5cclxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm92aWRlcnNQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDxUb2FzdGVyIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgcmljaENvbG9ycyAvPlxyXG4gICAgPC9BdXRoUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicG9zaXRpb24iLCJyaWNoQ29sb3JzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ConfirmationModal.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/ConfirmationModal.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfirmationModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExclamationTriangleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationTriangleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ExclamationTriangleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationTriangleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ConfirmationModal({ isOpen, onClose, onConfirm, title, message, itemName, itemId, confirmText = 'Delete', cancelText = 'Cancel', isLoading = false, variant = 'danger' }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmationModal.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"ConfirmationModal.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"ConfirmationModal.useEffect\"];\n        }\n    }[\"ConfirmationModal.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmationModal.useEffect\": ()=>{\n            const handleEscape = {\n                \"ConfirmationModal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && !isLoading) {\n                        onClose();\n                    }\n                }\n            }[\"ConfirmationModal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n            }\n            return ({\n                \"ConfirmationModal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                }\n            })[\"ConfirmationModal.useEffect\"];\n        }\n    }[\"ConfirmationModal.useEffect\"], [\n        isOpen,\n        isLoading,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    const handleBackdropClick = (e)=>{\n        if (e.target === e.currentTarget && !isLoading) {\n            onClose();\n        }\n    };\n    const handleConfirm = ()=>{\n        if (!isLoading) {\n            onConfirm();\n        }\n    };\n    const handleCancel = ()=>{\n        if (!isLoading) {\n            onClose();\n        }\n    };\n    const getVariantStyles = ()=>{\n        switch(variant){\n            case 'danger':\n                return {\n                    iconBg: 'bg-red-100',\n                    iconColor: 'text-red-600',\n                    confirmBg: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',\n                    confirmBgDisabled: 'bg-red-400'\n                };\n            case 'warning':\n                return {\n                    iconBg: 'bg-yellow-100',\n                    iconColor: 'text-yellow-600',\n                    confirmBg: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',\n                    confirmBgDisabled: 'bg-yellow-400'\n                };\n            case 'info':\n                return {\n                    iconBg: 'bg-blue-100',\n                    iconColor: 'text-blue-600',\n                    confirmBg: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',\n                    confirmBgDisabled: 'bg-blue-400'\n                };\n            default:\n                return {\n                    iconBg: 'bg-red-100',\n                    iconColor: 'text-red-600',\n                    confirmBg: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',\n                    confirmBgDisabled: 'bg-red-400'\n                };\n        }\n    };\n    const styles = getVariantStyles();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-200/50  transition-opacity\",\n                onClick: handleBackdropClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-full items-center justify-center p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left shadow-xl transition-all\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex-shrink-0 w-10 h-10 rounded-full ${styles.iconBg} flex items-center justify-center mr-3`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationTriangleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: `w-6 h-6 ${styles.iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    disabled: isLoading,\n                                    className: \"rounded-lg p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationTriangleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                itemName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-3 border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                            children: [\n                                                \"Item: \",\n                                                itemName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        itemId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"ID: \",\n                                                itemId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col-reverse sm:flex-row sm:justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    disabled: isLoading,\n                                    className: \"w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: cancelText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConfirm,\n                                    disabled: isLoading,\n                                    className: `w-full sm:w-auto px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:cursor-not-allowed ${isLoading ? styles.confirmBgDisabled : styles.confirmBg}`,\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Processing...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this) : confirmText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ConfirmationModal.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ConfirmationModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/apiClient */ \"(ssr)/./src/lib/api/apiClient.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null\n};\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        const userId = localStorage.getItem('userId');\n                        const name = localStorage.getItem('name');\n                        const email = localStorage.getItem('email');\n                        const phone = localStorage.getItem('phone');\n                        const isAdmin = localStorage.getItem('isAdmin');\n                        const createdAt = localStorage.getItem('createdAt');\n                        if (token && userId) {\n                            setAuthState({\n                                user: {\n                                    id: userId,\n                                    name: name || 'User',\n                                    email: email || '',\n                                    phone: phone || '',\n                                    isAdmin: isAdmin === 'true',\n                                    createdAt: createdAt || new Date().toISOString()\n                                },\n                                isAuthenticated: true,\n                                isLoading: false,\n                                error: null\n                            });\n                        } else {\n                            setAuthState({\n                                ...initialState,\n                                isLoading: false\n                            });\n                        }\n                    } catch (error) {\n                        console.log(error);\n                        setAuthState({\n                            ...initialState,\n                            isLoading: false\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post('/auth/login', {\n                email,\n                password\n            });\n            console.log(\"Login response:\", response.data);\n            if (response.data.status === 'success') {\n                const { user, token } = response.data.data;\n                // Store auth data in localStorage\n                localStorage.setItem('token', token);\n                localStorage.setItem('userId', user.id);\n                localStorage.setItem('name', user.name);\n                localStorage.setItem('email', user.email);\n                localStorage.setItem('phone', user.phone || '');\n                localStorage.setItem('isAdmin', user.isAdmin ? 'true' : 'false');\n                localStorage.setItem('createdAt', user.createdAt || new Date().toISOString());\n                // Update auth state\n                setAuthState({\n                    user: {\n                        id: user.id,\n                        name: user.name,\n                        email: user.email,\n                        phone: user.phone || '',\n                        isAdmin: user.isAdmin,\n                        createdAt: user.createdAt || new Date().toISOString()\n                    },\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged in successfully!');\n                // Check if user is admin\n                // if (user.isAdmin) {\n                router.push('/dashboard');\n            // } else {\n            //   throw new Error('Access denied. Admin privileges required.');\n            // }\n            } else {\n                throw new Error('Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Invalid email or password';\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n        }\n    };\n    // Logout function\n    const logout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('name');\n        localStorage.removeItem('email');\n        localStorage.removeItem('phone');\n        localStorage.removeItem('isAdmin');\n        localStorage.removeItem('createdAt');\n        // Reset auth state\n        setAuthState(initialState);\n        // Redirect to login\n        router.push('/auth/login');\n        sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged out successfully!');\n    };\n    // Function to directly set auth state (used by auth-callback)\n    const setAuthStateDirectly = (state)=>{\n        setAuthState(state);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            logout,\n            setAuthState: setAuthStateDirectly\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/apiClient.ts":
/*!**********************************!*\
  !*** ./src/lib/api/apiClient.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/productService.ts":
/*!***************************************!*\
  !*** ./src/lib/api/productService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n\nclass ProductService {\n    /**\n   * Get all products with optional filters\n   */ async getProducts(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            // Add filters to query params\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.category) {\n                params.append('category', filters.category);\n            }\n            if (filters.type) {\n                params.append('type', filters.type);\n            }\n            if (filters.minPrice !== undefined) {\n                params.append('price[gte]', filters.minPrice.toString());\n            }\n            if (filters.maxPrice !== undefined) {\n                params.append('price[lte]', filters.maxPrice.toString());\n            }\n            if (filters.inStock !== undefined) {\n                params.append('stock', filters.inStock.toString());\n            }\n            if (filters.sort) {\n                params.append('sort', filters.sort);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `/products${queryString ? `?${queryString}` : ''}`;\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get a single product by ID\n   */ async getProduct(id) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/products/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new product with FormData (for file uploads)\n   */ async createProduct(formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            // Use Next.js API proxy to avoid CORS issues\n            const response = await fetch('/api/products', {\n                method: 'POST',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to create product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error creating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update a product with FormData (for file uploads)\n   */ async updateProduct(id, formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'PATCH',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to update product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error updating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a product\n   */ async deleteProduct(id) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for consistent authorization handling\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Product deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get product categories\n   */ async getCategories() {\n        try {\n            const response = await this.getProducts();\n            const categories = [\n                ...new Set(response.data.products.map((product)=>product.category))\n            ];\n            return categories.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            return [];\n        }\n    }\n}\nconst productService = new ProductService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (productService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/productService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/**\r\n * Format a date to a readable string\r\n */ function formatDate(date) {\n    try {\n        const dateObj = date instanceof Date ? date : new Date(date);\n        // Check if the date is valid\n        if (isNaN(dateObj.getTime())) {\n            return 'Invalid Date';\n        }\n        return new Intl.DateTimeFormat('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        }).format(dateObj);\n    } catch (error) {\n        console.error('Error formatting date:', error);\n        return 'Invalid Date';\n    }\n}\n/**\r\n * Format a number as currency\r\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(amount);\n}\n/**\r\n * Truncate a string to a specified length\r\n */ function truncateString(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + '...';\n}\n/**\r\n * Generate a random ID\r\n */ function generateId() {\n    return Math.random().toString(36).substring(2, 9);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/imageUtils.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/imageUtils.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getImageUrls: () => (/* binding */ getImageUrls),\n/* harmony export */   handleImageError: () => (/* binding */ handleImageError)\n/* harmony export */ });\n/**\n * Utility functions for handling image URLs\n */ /**\n * Get the proper image URL for display\n * @param imageFilename - The filename stored in the database\n * @returns The proper URL to access the image\n */ function getImageUrl(imageFilename) {\n    // Return placeholder if no filename\n    if (!imageFilename) {\n        return '/placeholder-product.svg';\n    }\n    // If it's already a full URL, return as is\n    if (imageFilename.startsWith('http://') || imageFilename.startsWith('https://')) {\n        return imageFilename;\n    }\n    // If it's a relative path starting with /, return as is\n    if (imageFilename.startsWith('/')) {\n        return imageFilename;\n    }\n    // For GridFS filenames, use the image proxy\n    return `/api/images/${imageFilename}`;\n}\n/**\n * Get multiple image URLs\n * @param imageFilenames - Array of filenames\n * @returns Array of proper URLs\n */ function getImageUrls(imageFilenames) {\n    return imageFilenames.filter(Boolean).map((filename)=>getImageUrl(filename));\n}\n/**\n * Handle image error by setting a placeholder\n * @param event - The error event from img element\n */ function handleImageError(event) {\n    const target = event.target;\n    if (target.src !== '/placeholder-product.svg') {\n        target.src = '/placeholder-product.svg';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/imageUtils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/sonner","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();