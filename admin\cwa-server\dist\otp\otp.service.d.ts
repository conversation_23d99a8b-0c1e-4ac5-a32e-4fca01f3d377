import { CreateOtpDto } from './dto/create-otp.dto';
import { Otp } from './schema/otp.schema';
import { Model } from 'mongoose';
export declare class OtpService {
    private otpModel;
    constructor(otpModel: Model<Otp>);
    create(createOtpDto: CreateOtpDto): Promise<import("mongoose").Document<unknown, {}, Otp, {}> & Otp & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    findOne(email: string, otp: string): Promise<import("mongoose").Document<unknown, {}, Otp, {}> & Otp & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    delete(otpId: string): Promise<import("mongoose").Document<unknown, {}, Otp, {}> & Otp & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
}
