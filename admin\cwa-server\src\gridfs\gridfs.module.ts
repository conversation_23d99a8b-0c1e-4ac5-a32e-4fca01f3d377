import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { GridFSService } from './gridfs.service';
import { GridFSProductController } from './gridfs-product.controller';
import { ImageController } from './image.controller';
import { GridFSUploadMiddleware } from './gridfs-upload.middleware';
import { MulterConfigService } from './multer-config.service';
import { GridFSUploadInterceptor } from './interceptors/gridfs-upload.interceptor';
import { ProductsModule } from '../products/products.module';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ConfigModule,
    ProductsModule, // Import ProductsModule to use ProductsService
    AuthModule, // Import AuthModule to use AdminGuard
    UsersModule, // Import UsersModule for AdminGuard dependency
  ],
  controllers: [GridFSProductController, ImageController],
  providers: [GridFSService, GridFSUploadMiddleware, MulterConfigService, GridFSUploadInterceptor],
  exports: [GridFSService, GridFSUploadMiddleware, MulterConfigService, GridFSUploadInterceptor],
})
export class GridFSModule {}
