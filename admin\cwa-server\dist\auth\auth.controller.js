"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const createUser_dto_1 = require("../users/dto/createUser.dto");
const signIn_dto_1 = require("./dto/signIn.dto");
const forget_password_dto_1 = require("./dto/forget-password.dto");
const reset_password_dto_1 = require("./dto/reset-password.dto");
const change_password_dto_1 = require("./dto/change-password.dto");
const verify_otp_dto_1 = require("./dto/verify-otp.dto");
const auth_guard_1 = require("./auth.guard");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async signUp(signUpDto) {
        try {
            const result = await this.authService.signUp(signUpDto);
            return {
                status: "success",
                data: {
                    user: {
                        id: result._id,
                        name: result.name,
                        email: result.email,
                        phone: result.phone,
                    },
                    token: result.accessToken,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'Registration failed',
            }, error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async signIn(signInDto) {
        try {
            const result = await this.authService.signIn(signInDto);
            return {
                status: "success",
                data: {
                    user: {
                        id: result._id,
                        name: result.name,
                        email: result.email,
                        phone: result.phone,
                    },
                    token: result.accessToken,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'Login failed',
            }, error.status || common_1.HttpStatus.UNAUTHORIZED);
        }
    }
    async forgotPassword(forgotPasswordDto) {
        try {
            const result = await this.authService.forgotPassword(forgotPasswordDto);
            return {
                status: "success",
                message: result.message,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'Failed to send password reset email',
            }, error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async verifyOtp(verifyOtpDto) {
        try {
            const result = await this.authService.verifyOtp(verifyOtpDto);
            return {
                status: "success",
                message: "OTP verified successfully",
                resetToken: result.resetToken,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'OTP verification failed',
            }, error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async resetPassword(resetPasswordDto) {
        try {
            await this.authService.resetPassword(resetPasswordDto);
            return {
                status: "success",
                message: "Password reset successful",
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'Password reset failed',
            }, error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async changePassword(req, changePasswordDto) {
        try {
            const userId = req.user._id;
            const result = await this.authService.changePassword(userId, changePasswordDto);
            return {
                status: "success",
                message: result.message,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: "fail",
                message: error.message || 'Password change failed',
            }, error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)("register"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createUser_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
__decorate([
    (0, common_1.Post)("login"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [signIn_dto_1.signInDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signIn", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [forget_password_dto_1.ForgotPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('verify-otp'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [verify_otp_dto_1.VerifyOtpDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyOtp", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [reset_password_dto_1.ResetPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('change-password'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, change_password_dto_1.ChangePasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map