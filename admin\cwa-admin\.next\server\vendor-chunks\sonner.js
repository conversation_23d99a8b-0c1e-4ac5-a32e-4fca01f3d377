"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}[data-sonner-toaster][data-lifted=true]{transform:translateY(-8px)}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme :  false ? 0 : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": ({ id })=>id !== toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (true) return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-lifted\": expanded && toasts.length > 1 && !expand,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;