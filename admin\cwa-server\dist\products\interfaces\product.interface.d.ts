import { Document } from 'mongoose';
export interface IProduct extends Document {
    name: string;
    type: 'regular' | 'featured' | 'sale' | 'new';
    price: number;
    image: string;
    description: string;
    quantity: number;
    stock: boolean;
    category: string;
    images: string[];
    video?: string;
    discount?: string;
    id?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface IProductResponse {
    status: string;
    data?: {
        product?: IProduct;
        products?: IProduct[];
    };
    results?: number;
    message?: string;
    error?: string;
}
export interface IProductQuery {
    page?: number;
    limit?: number;
    sort?: string;
    fields?: string;
    category?: string;
    type?: string;
    stock?: boolean;
    price?: {
        gte?: number;
        lte?: number;
        gt?: number;
        lt?: number;
    };
}
