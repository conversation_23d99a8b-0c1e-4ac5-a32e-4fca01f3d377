import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { AdminGuard } from '../auth/admin.guard';
import {
  IUserResponse,
  IUserQuery
} from './interfaces/users.interface';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  async createUser(@Body() createUserDto: CreateUserDto): Promise<IUserResponse> {
    try {
      const user = await this.usersService.createUser(createUserDto);
      return user;
    } catch (error) {
      throw new HttpException(
        {
          error: error.message || 'Failed to create user',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async getUsers(@Query() query: IUserQuery): Promise<IUserResponse[]> {
    try {
      const result = await this.usersService.findAll(query);
      return result.users;
    } catch (error) {
      throw new HttpException(
        {
          error: error.message || 'Failed to fetch users',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getUser(@Param('id') id: string): Promise<IUserResponse> {
    try {
      const user = await this.usersService.findById(id);
      return user;
    } catch (error) {
      throw new HttpException(
        {
          error: error.message || 'Failed to fetch user',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<IUserResponse> {
    try {
      const user = await this.usersService.updateUser(id, updateUserDto);
      return user;
    } catch (error) {
      throw new HttpException(
        {
          error: error.message || 'Failed to update user',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  async deleteUser(@Param('id') id: string): Promise<{ message: string }> {
    try {
      await this.usersService.deleteUser(id);
      return {
        message: 'User deleted',
      };
    } catch (error) {
      throw new HttpException(
        {
          error: error.message || 'Failed to delete user',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}