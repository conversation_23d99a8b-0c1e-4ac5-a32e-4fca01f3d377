'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import productService from '@/lib/api/productService';
import { Product } from '@/types/user';
import FormField, { Input, Textarea, Select, Checkbox } from '@/components/ui/FormField';
import FileUpload, { MultipleFileUpload } from '@/components/ui/FileUpload';
import { useFormValidation, productValidationRules } from '@/hooks/useFormValidation';
import { toast } from 'sonner';
import { ArrowLeftIcon, EyeIcon, BookmarkIcon } from '@heroicons/react/24/outline';
import { ProductFormSkeleton } from '@/components/ui/LoadingSkeleton';

interface ProductFormData {
  name: string;
  type: string;
  price: number;
  description: string;
  quantity: number;
  category: string;
  stock: boolean;
  discount?: string;
  image: File | null;
  additionalImages: File[];
  currentImage?: string;
  currentAdditionalImages?: string[];
  [key: string]: unknown;
}

export default function EditProduct() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    type: 'regular',
    price: 0,
    description: '',
    quantity: 0,
    category: '',
    stock: true,
    discount: '',
    image: null,
    additionalImages: [],
    currentImage: '',
    currentAdditionalImages: []
  });

  // Form validation
  const { errors, validateField, validateForm, clearError } = useFormValidation(productValidationRules);

  // Form state tracking
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeSection, setActiveSection] = useState<'basic' | 'pricing' | 'inventory' | 'images'>('basic');

  const productTypes = ['regular', 'featured', 'sale', 'new'];
  const categories = [
    'Furniture',
    'Home Decor',
    'Kitchen Items',
    'Garden',
    'Bedroom',
    'Living Room',
    'Office',
    'Storage'
  ];

  // Fetch product data on component mount
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setFetchLoading(true);
        setError(null);
        
        const response = await productService.getProduct(productId);
        
        if (response.status === 'success') {
          const productData = response.data;
          setProduct(productData);
          
          // Populate form with existing data
          setFormData({
            name: productData.name || '',
            type: productData.type || 'regular',
            price: productData.price || 0,
            description: productData.description || '',
            quantity: productData.quantity || 0,
            category: productData.category || '',
            stock: productData.stock !== undefined ? productData.stock : true,
            discount: productData.discount || '',
            image: null,
            additionalImages: [],
            currentImage: productData.image || '',
            currentAdditionalImages: productData.images || []
          });
        } else {
          throw new Error('Failed to fetch product');
        }
      } catch (err: unknown) {
        console.error('Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch product');
      } finally {
        setFetchLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked :
                     type === 'number' ? parseFloat(value) || 0 : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Clear validation error when user starts typing
    if (errors[name]) {
      clearError(name);
    }

    // Real-time validation for certain fields
    if (['name', 'price', 'quantity'].includes(name)) {
      const error = validateField(name, newValue);
      if (!error) {
        clearError(name);
      }
    }

    setHasUnsavedChanges(true);
  };

  const handleImageChange = (file: File | null) => {
    setFormData(prev => ({ ...prev, image: file }));
    setHasUnsavedChanges(true);
  };

  const handleAdditionalImagesChange = (files: File[]) => {
    setFormData(prev => ({ ...prev, additionalImages: files }));
    setHasUnsavedChanges(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const isValid = validateForm(formData);
    if (!isValid) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create FormData for file upload
      const submitFormData = new FormData();
      
      // Add all form fields
      submitFormData.append('name', formData.name.trim());
      submitFormData.append('type', formData.type);
      submitFormData.append('price', formData.price.toString());
      submitFormData.append('description', formData.description.trim());
      submitFormData.append('quantity', formData.quantity.toString());
      submitFormData.append('category', formData.category);
      submitFormData.append('stock', formData.stock.toString());
      
      if (formData.discount && formData.discount.trim()) {
        submitFormData.append('discount', formData.discount.trim());
      }

      // Handle image upload
      if (formData.image) {
        submitFormData.append('image', formData.image);
      } else {
        // Keep existing image
        submitFormData.append('keepExistingImage', 'true');
      }

      // Handle additional images
      if (formData.additionalImages.length > 0) {
        formData.additionalImages.forEach((file) => {
          submitFormData.append('additionalImages', file);
        });
      } else {
        // Keep existing additional images
        submitFormData.append('keepExistingAdditionalImages', 'true');
      }

      // Submit to API
      const response = await productService.updateProduct(productId, submitFormData);

      if (response.status === 'success') {
        console.log('Product updated successfully:', response);
        router.push('/products');
      } else {
        throw new Error('Failed to update product');
      }
    } catch (err: unknown) {
      console.error('Error updating product:', err);
      setError(err instanceof Error ? err.message : 'Failed to update product');
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            <ProductFormSkeleton />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !product) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full max-w-full overflow-x-hidden">
          <div className="space-y-6 p-4 md:p-6">
            <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
              <div className="text-center py-12">
                <div className="text-6xl mb-4">❌</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Product</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        <div className="space-y-6 p-4 md:p-6">
          {/* Header */}
          <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              <div>
                <h2 className="text-xl md:text-2xl font-bold text-gray-900">
                  Edit Product
                </h2>
                <p className="text-gray-600 mt-1 text-sm md:text-base">
                  Update product information and settings
                </p>
                {product && (
                  <p className="text-xs md:text-sm text-gray-500 mt-1">
                    Editing: {product.name}
                  </p>
                )}
              </div>
              <button
                type="button"
                onClick={() => router.back()}
                className="w-full lg:w-auto bg-gray-500 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 text-sm md:text-base"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span>Back to Products</span>
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <div className="bg-white rounded-lg shadow-sm">
            {/* Form Navigation Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex overflow-x-auto px-4 sm:px-6 pt-4 sm:pt-6 pb-0" aria-label="Form sections">
                <div className="flex space-x-4 sm:space-x-8 min-w-max">
                  {([
                    { id: 'basic' as const, label: 'Basic Info', icon: '📝', shortLabel: 'Basic' },
                    { id: 'pricing' as const, label: 'Pricing', icon: '💰', shortLabel: 'Price' },
                    { id: 'inventory' as const, label: 'Inventory', icon: '📦', shortLabel: 'Stock' },
                    { id: 'images' as const, label: 'Images', icon: '🖼️', shortLabel: 'Images' }
                  ] as const).map((section) => (
                    <button
                      key={section.id}
                      type="button"
                      onClick={() => setActiveSection(section.id)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap touch-target ${
                        activeSection === section.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <span className="mr-1 sm:mr-2">{section.icon}</span>
                      <span className="hidden sm:inline">{section.label}</span>
                      <span className="sm:hidden">{section.shortLabel}</span>
                    </button>
                  ))}
                </div>
              </nav>
            </div>

            <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-6 sm:space-y-8">
              {/* Unsaved Changes Warning */}
              {hasUnsavedChanges && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 sm:p-4">
                  <div className="flex items-start sm:items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-4 w-4 sm:h-5 sm:w-5 text-amber-400 mt-0.5 sm:mt-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-2 sm:ml-3">
                      <p className="text-xs sm:text-sm text-amber-800">You have unsaved changes. Don&apos;t forget to save your work!</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Information Section */}
              {activeSection === 'basic' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <span className="text-lg">📝</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                      <p className="text-sm text-gray-500">Essential product details and description</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <FormField
                      label="Product Name"
                      id="name"
                      required
                      error={errors.name}
                      hint="Enter a clear, descriptive product name"
                      className="sm:col-span-2"
                    >
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="e.g., Wooden Coffee Table"
                        error={!!errors.name}
                      />
                    </FormField>

                    <FormField
                      label="Category"
                      id="category"
                      required
                      error={errors.category}
                      hint="Select the most appropriate category"
                    >
                      <Select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        error={!!errors.category}
                      >
                        <option value="">Select a category</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </Select>
                    </FormField>

                    <FormField
                      label="Product Type"
                      id="type"
                      required
                      error={errors.type}
                      hint="Choose the product classification"
                    >
                      <Select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        error={!!errors.type}
                      >
                        {productTypes.map(type => (
                          <option key={type} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </option>
                        ))}
                      </Select>
                    </FormField>
                  </div>

                  <FormField
                    label="Description"
                    id="description"
                    error={errors.description}
                    hint="Provide a detailed description of the product"
                    className="col-span-full"
                  >
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Describe the product features, materials, dimensions, etc."
                      rows={4}
                      error={!!errors.description}
                    />
                  </FormField>
                </div>
              )}

              {/* Pricing Section */}
              {activeSection === 'pricing' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <span className="text-lg">💰</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Pricing Information</h3>
                      <p className="text-sm text-gray-500">Set product pricing and discount options</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <FormField
                      label="Price"
                      id="price"
                      required
                      error={errors.price}
                      hint="Enter the product price in your local currency"
                    >
                      <Input
                        id="price"
                        name="price"
                        type="number"
                        value={formData.price}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        error={!!errors.price}
                      />
                    </FormField>

                    <FormField
                      label="Discount Percentage"
                      id="discount"
                      error={errors.discount}
                      hint="Optional discount percentage (0-100)"
                    >
                      <Input
                        id="discount"
                        name="discount"
                        type="number"
                        value={formData.discount}
                        onChange={handleInputChange}
                        min="0"
                        max="100"
                        step="1"
                        placeholder="0"
                        error={!!errors.discount}
                      />
                    </FormField>
                  </div>
                </div>
              )}

              {/* Inventory Section */}
              {activeSection === 'inventory' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <span className="text-lg">📦</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Inventory Management</h3>
                      <p className="text-sm text-gray-500">Manage stock levels and availability</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <FormField
                      label="Quantity"
                      id="quantity"
                      required
                      error={errors.quantity}
                      hint="Current stock quantity"
                    >
                      <Input
                        id="quantity"
                        name="quantity"
                        type="number"
                        value={formData.quantity}
                        onChange={handleInputChange}
                        min="0"
                        step="1"
                        placeholder="0"
                        error={!!errors.quantity}
                      />
                    </FormField>

                    <FormField
                      label="Stock Status"
                      id="stock"
                      hint="Toggle product availability"
                    >
                      <div className="pt-2">
                        <Checkbox
                          id="stock"
                          name="stock"
                          checked={formData.stock}
                          onChange={handleInputChange}
                          label="Product is in stock and available for purchase"
                        />
                      </div>
                    </FormField>
                  </div>
                </div>
              )}

              {/* Images Section */}
              {activeSection === 'images' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <span className="text-lg">🖼️</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Product Images</h3>
                      <p className="text-sm text-gray-500">Upload main and additional product images</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <FormField
                      label="Main Product Image"
                      id="image"
                      hint="Primary image that will be displayed prominently"
                    >
                      <FileUpload
                        onFileSelect={handleImageChange}
                        currentFile={formData.image}
                        currentImageUrl={formData.currentImage}
                        accept="image/*"
                        maxSize={5}
                        label="Upload Main Image"
                        hint="PNG, JPG, GIF up to 5MB"
                      />
                    </FormField>

                    <FormField
                      label="Additional Images"
                      id="additionalImages"
                      hint="Upload up to 5 additional product images"
                    >
                      <MultipleFileUpload
                        onFilesSelect={handleAdditionalImagesChange}
                        currentFiles={formData.additionalImages}
                        currentImageUrls={formData.currentAdditionalImages}
                        accept="image/*"
                        maxSize={5}
                        maxFiles={5}
                        label="Upload Additional Images"
                        hint="PNG, JPG, GIF up to 5MB each (max 5 files)"
                      />
                    </FormField>
                  </div>
                </div>
              )}

              {/* Form Navigation and Actions */}
              <div className="flex flex-col gap-4 pt-6 sm:pt-8 border-t border-gray-200">
                {/* Mobile Navigation Buttons */}
                <div className="flex justify-between sm:hidden">
                  {activeSection !== 'basic' ? (
                    <button
                      type="button"
                      onClick={() => {
                        const sections: Array<'basic' | 'pricing' | 'inventory' | 'images'> = ['basic', 'pricing', 'inventory', 'images'];
                        const currentIndex = sections.indexOf(activeSection);
                        if (currentIndex > 0) {
                          setActiveSection(sections[currentIndex - 1]);
                        }
                      }}
                      className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors touch-target"
                    >
                      <ArrowLeftIcon className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                  ) : (
                    <div></div>
                  )}

                  {activeSection !== 'images' && (
                    <button
                      type="button"
                      onClick={() => {
                        const sections: Array<'basic' | 'pricing' | 'inventory' | 'images'> = ['basic', 'pricing', 'inventory', 'images'];
                        const currentIndex = sections.indexOf(activeSection);
                        if (currentIndex < sections.length - 1) {
                          setActiveSection(sections[currentIndex + 1]);
                        }
                      }}
                      className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors touch-target"
                    >
                      Next
                      <ArrowLeftIcon className="w-4 h-4 ml-2 rotate-180" />
                    </button>
                  )}
                </div>

                {/* Desktop Navigation and Action Buttons */}
                <div className="hidden sm:flex sm:flex-row gap-4">
                  {/* Navigation Buttons */}
                  <div className="flex space-x-2 flex-1">
                    {activeSection !== 'basic' && (
                      <button
                        type="button"
                        onClick={() => {
                          const sections: Array<'basic' | 'pricing' | 'inventory' | 'images'> = ['basic', 'pricing', 'inventory', 'images'];
                          const currentIndex = sections.indexOf(activeSection);
                          if (currentIndex > 0) {
                            setActiveSection(sections[currentIndex - 1]);
                          }
                        }}
                        className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      >
                        <ArrowLeftIcon className="w-4 h-4 mr-2" />
                        Previous
                      </button>
                    )}

                    {activeSection !== 'images' && (
                      <button
                        type="button"
                        onClick={() => {
                          const sections: Array<'basic' | 'pricing' | 'inventory' | 'images'> = ['basic', 'pricing', 'inventory', 'images'];
                          const currentIndex = sections.indexOf(activeSection);
                          if (currentIndex < sections.length - 1) {
                            setActiveSection(sections[currentIndex + 1]);
                          }
                        }}
                        className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      >
                        Next
                        <ArrowLeftIcon className="w-4 h-4 ml-2 rotate-180" />
                      </button>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => router.push(`/products`)}
                      className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                    >
                      <EyeIcon className="w-4 h-4 mr-2" />
                      View All Products
                    </button>

                    <button
                      type="button"
                      onClick={() => router.back()}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
                    >
                      Cancel
                    </button>

                    <button
                      type="submit"
                      disabled={loading}
                      className="flex items-center px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {loading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Updating...
                        </>
                      ) : (
                        <>
                          <BookmarkIcon className="w-4 h-4 mr-2" />
                          Update Product
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* Mobile Action Buttons */}
                <div className="flex flex-col space-y-3 sm:hidden">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full flex items-center justify-center px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Updating...
                      </>
                    ) : (
                      <>
                        <BookmarkIcon className="w-4 h-4 mr-2" />
                        Update Product
                      </>
                    )}
                  </button>

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => router.push(`/products`)}
                      className="flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors touch-target"
                    >
                      <EyeIcon className="w-4 h-4 mr-2" />
                      View All
                    </button>

                    <button
                      type="button"
                      onClick={() => router.back()}
                      className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors touch-target"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
