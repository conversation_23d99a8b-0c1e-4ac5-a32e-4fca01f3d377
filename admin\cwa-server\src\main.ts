import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as cors from 'cors';
import { GridFSService } from './gridfs/gridfs.service';
import { Request, Response, NextFunction } from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule,{bodyParser:true});

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );
app.use(cors(
  {
    origin:'*',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  }
));

  // Add image serving middleware before global prefix
  const gridfsService = app.get(GridFSService);

  app.use((req: Request, res: Response, next: NextFunction) => {
    // Check if this is an image request at root level
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i;
    if (req.method === 'GET' && imageExtensions.test(req.path)) {
      const filename = req.path.substring(1); // Remove leading slash

      console.log('Image request for:', filename);

      // Validate filename
      if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return next();
      }

      // Try to serve the image
      gridfsService.findFileByFilename(filename)
        .then(file => {
          if (!file) {
            console.log('File not found:', filename);
            return next();
          }

          console.log('File found:', file.filename);

          // Set headers
          res.set({
            'Content-Type': file.metadata?.mimetype || 'image/jpeg',
            'Cache-Control': 'public, max-age=3600',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY'
          });

          // Stream the file
          const readStream = gridfsService.createReadStreamByFilename(filename);

          readStream.on('error', (error) => {
            console.error('GridFS read stream error:', error);
            if (!res.headersSent) {
              return next();
            }
          });

          readStream.pipe(res);
        })
        .catch(error => {
          console.error('Error serving image:', error);
          next();
        });
    } else {
      next();
    }
  });


  // Global prefix
  app.setGlobalPrefix('api');



  await app.listen(4000);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(`Swagger documentation available at: ${await app.getUrl()}/api/docs`);
}
bootstrap();
