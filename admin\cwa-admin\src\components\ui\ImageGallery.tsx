'use client';

import { useState, useEffect, useCallback } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface ImageGalleryProps {
  images: string[];
  alt: string;
  className?: string;
}

export default function ImageGallery({ images, alt, className = '' }: ImageGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean[]>(new Array(images.length).fill(true));

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isLightboxOpen) return;
    
    switch (e.key) {
      case 'Escape':
        setIsLightboxOpen(false);
        break;
      case 'ArrowLeft':
        setSelectedIndex(prev => prev > 0 ? prev - 1 : images.length - 1);
        break;
      case 'ArrowRight':
        setSelectedIndex(prev => prev < images.length - 1 ? prev + 1 : 0);
        break;
    }
  }, [isLightboxOpen, images.length]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Prevent body scroll when lightbox is open
  useEffect(() => {
    if (isLightboxOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isLightboxOpen]);

  const handleImageLoad = (index: number) => {
    setIsLoading(prev => {
      const newLoading = [...prev];
      newLoading[index] = false;
      return newLoading;
    });
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>, index: number) => {
    const target = e.target as HTMLImageElement;
    target.src = '/placeholder-product.svg';
    handleImageLoad(index);
  };

  const openLightbox = (index: number) => {
    setSelectedIndex(index);
    setIsLightboxOpen(true);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedIndex(prev => prev > 0 ? prev - 1 : images.length - 1);
    } else {
      setSelectedIndex(prev => prev < images.length - 1 ? prev + 1 : 0);
    }
  };

  if (!images || images.length === 0) {
    return (
      <div className={`aspect-square bg-gray-100 rounded-xl flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-400">
          <div className="w-16 h-16 mx-auto mb-2 opacity-50">
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </svg>
          </div>
          <p className="text-sm">No images available</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`space-y-4 ${className}`}>
        {/* Main Image */}
        <div className="relative aspect-square bg-gray-100 rounded-xl overflow-hidden group">
          {isLoading[selectedIndex] && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          )}
          
          <img
            src={images[selectedIndex]}
            alt={`${alt} - Image ${selectedIndex + 1}`}
            className="w-full h-full object-cover transition-opacity duration-300"
            onLoad={() => handleImageLoad(selectedIndex)}
            onError={(e) => handleImageError(e, selectedIndex)}
          />
          
          {/* Zoom overlay */}
          <button
            onClick={() => openLightbox(selectedIndex)}
            className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100"
            aria-label="Open image in lightbox"
          >
            <div className="bg-white/90 backdrop-blur-sm rounded-full p-2">
              <MagnifyingGlassIcon className="w-6 h-6 text-gray-700" />
            </div>
          </button>

          {/* Navigation arrows for main image */}
          {images.length > 1 && (
            <>
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                aria-label="Previous image"
              >
                <ChevronLeftIcon className="w-5 h-5 text-gray-700" />
              </button>
              
              <button
                onClick={() => navigateImage('next')}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                aria-label="Next image"
              >
                <ChevronRightIcon className="w-5 h-5 text-gray-700" />
              </button>
            </>
          )}

          {/* Image counter */}
          {images.length > 1 && (
            <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
              {selectedIndex + 1} / {images.length}
            </div>
          )}
        </div>

        {/* Thumbnails */}
        {images.length > 1 && (
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedIndex(index)}
                className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                  selectedIndex === index 
                    ? 'border-blue-500 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                aria-label={`View image ${index + 1}`}
              >
                {isLoading[index] && (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  </div>
                )}
                <img
                  src={image}
                  alt={`${alt} thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  onLoad={() => handleImageLoad(index)}
                  onError={(e) => handleImageError(e, index)}
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Lightbox */}
      {isLightboxOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
          role="dialog"
          aria-modal="true"
          aria-labelledby="lightbox-title"
          aria-describedby="lightbox-description"
        >
          {/* Screen reader announcements */}
          <div className="sr-only">
            <h2 id="lightbox-title">Image Gallery Lightbox</h2>
            <p id="lightbox-description">
              Viewing image {selectedIndex + 1} of {images.length}. Use arrow keys to navigate, escape to close.
            </p>
          </div>

          {/* Close button */}
          <button
            onClick={() => setIsLightboxOpen(false)}
            className="absolute top-4 right-4 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
            aria-label="Close lightbox (Escape key)"
          >
            <XMarkIcon className="w-6 h-6 sm:w-8 sm:h-8" />
          </button>

          {/* Navigation */}
          {images.length > 1 && (
            <>
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
                aria-label={`Previous image (${selectedIndex} of ${images.length})`}
                disabled={selectedIndex === 0}
              >
                <ChevronLeftIcon className="w-6 h-6 sm:w-8 sm:h-8" />
              </button>

              <button
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
                aria-label={`Next image (${selectedIndex + 2} of ${images.length})`}
                disabled={selectedIndex === images.length - 1}
              >
                <ChevronRightIcon className="w-6 h-6 sm:w-8 sm:h-8" />
              </button>
            </>
          )}

          {/* Main lightbox image */}
          <div className="max-w-4xl max-h-full" role="img" aria-label={`${alt} - Image ${selectedIndex + 1} of ${images.length}`}>
            <img
              src={images[selectedIndex]}
              alt={`${alt} - Image ${selectedIndex + 1} of ${images.length}`}
              className="max-w-full max-h-full object-contain"
              onError={(e) => handleImageError(e, selectedIndex)}
            />
          </div>

          {/* Image info */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 text-white text-center">
            <p className="text-sm opacity-75" aria-live="polite">
              Image {selectedIndex + 1} of {images.length}
            </p>
          </div>
        </div>
      )}
    </>
  );
}
