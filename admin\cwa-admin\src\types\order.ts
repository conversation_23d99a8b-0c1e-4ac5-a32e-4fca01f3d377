export interface OrderProduct {
  _id: string;
  name: string;
  price: number;
  image?: string;
  description?: string;
  category?: string;
  stock?: number;
  quantity?: number;
}

export interface OrderItem {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  _id: string;
  description?: string;
  category?: string;
}

export interface CustomerInfo {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface ShippingAddress {
  name?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
}

export interface Order {
  _id: string;
  user: string;
  products?: OrderProduct[]; // Legacy field for backward compatibility
  items?: OrderItem[]; // New field from actual API
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  metadata?: {
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    shippingAddress?: string;
    paymentMethod?: string;
    notes?: string;
    customer?: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
    };
    shipping?: {
      address?: string;
      city?: string;
      postalCode?: string;
    };
    subtotal?: number;
    discount?: number;
  };
  // Additional fields for better customer info handling
  customer?: CustomerInfo;
  shippingAddress?: ShippingAddress;
  paymentMethod?: string;
  paymentStatus?: 'pending' | 'paid' | 'failed';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrdersResponse {
  status: string;
  results: number;
  total: number;
  totalPages: number;
  currentPage: number;
  data: {
    orders: Order[];
  };
}

export interface OrderFilters {
  search?: string;
  status?: string;
  dateFilter?: string;
  page?: number;
  limit?: number;
  user?: string;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  processingOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
}
