/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/view/[id]/page";
exports.ids = ["app/products/view/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&page=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fview%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&page=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fview%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/view/[id]/page.tsx */ \"(rsc)/./src/app/products/view/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        'view',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/view/[id]/page\",\n        pathname: \"/products/view/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&page=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fview%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(rsc)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3QlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1nZWlzdC1zYW5zJTVDJTIyJTJDJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyZ2Vpc3RTYW5zJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3RfTW9ubyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWdlaXN0LW1vbm8lNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJnZWlzdE1vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQUxJJTIwQ09NUFVURVJTJTVDJTVDRGVza3RvcCU1QyU1Q2FkbWluJTVDJTVDY3dhLWFkbWluJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQUxJJTIwQ09NUFVURVJTJTVDJTVDRGVza3RvcCU1QyU1Q2FkbWluJTVDJTVDY3dhLWFkbWluJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q1Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBZ0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBTEkgQ09NUFVURVJTXFxcXERlc2t0b3BcXFxcYWRtaW5cXFxcY3dhLWFkbWluXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxQcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/view/[id]/page.tsx */ \"(rsc)/./src/app/products/view/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb2R1Y3RzJTVDJTVDdmlldyU1QyU1QyU1QmlkJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3RzXFxcXHZpZXdcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQUxJIENPTVBVVEVSU1xcRGVza3RvcFxcYWRtaW5cXGN3YS1hZG1pblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a96de9275224\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFMSSBDT01QVVRFUlNcXERlc2t0b3BcXGFkbWluXFxjd2EtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE5NmRlOTI3NTIyNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/Providers */ \"(rsc)/./src/components/providers/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"CWA Admin Dashboard\",\n    description: \"Admin dashboard for CWA application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDa0M7QUFZbEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLHVFQUFTQTswQkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCBQcm92aWRlcnMgZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNXQSBBZG1pbiBEYXNoYm9hcmRcIixcbiAgZGVzY3JpcHRpb246IFwiQWRtaW4gZGFzaGJvYXJkIGZvciBDV0EgYXBwbGljYXRpb25cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/view/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/products/view/[id]/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\app\\products\\view\\[id]\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\components\\providers\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./lib/api/apiClient.ts":
/*!******************************!*\
  !*** ./lib/api/apiClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(ssr)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/view/[id]/page.tsx */ \"(ssr)/./src/app/products/view/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb2R1Y3RzJTVDJTVDdmlldyU1QyU1QyU1QmlkJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3RzXFxcXHZpZXdcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cview%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/products/view/[id]/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/products/view/[id]/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,HeartIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/TagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,CheckCircleIcon,CubeIcon,TagIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CalendarIcon.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api_productService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/productService */ \"(ssr)/./src/lib/api/productService.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/imageUtils */ \"(ssr)/./src/lib/utils/imageUtils.ts\");\n/* harmony import */ var _components_ui_ImageGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ImageGallery */ \"(ssr)/./src/components/ui/ImageGallery.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ProductDetailPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const productId = params.id;\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch product data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            const fetchProduct = {\n                \"ProductDetailPage.useEffect.fetchProduct\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await _lib_api_productService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getProduct(productId);\n                        if (response.status === 'success') {\n                            setProduct(response.data);\n                        } else {\n                            throw new Error('Failed to fetch product');\n                        }\n                    } catch (err) {\n                        console.error('Error fetching product:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to fetch product');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductDetailPage.useEffect.fetchProduct\"];\n            if (productId) {\n                fetchProduct();\n            }\n        }\n    }[\"ProductDetailPage.useEffect\"], [\n        productId\n    ]);\n    // Get product type configuration with functional icons\n    const getTypeConfig = (type)=>{\n        switch(type){\n            case 'featured':\n                return {\n                    label: 'Featured Product',\n                    className: 'bg-purple-50 text-purple-700 border-purple-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 17\n                    }, this)\n                };\n            case 'sale':\n                return {\n                    label: 'On Sale',\n                    className: 'bg-red-50 text-red-700 border-red-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 17\n                    }, this)\n                };\n            case 'new':\n                return {\n                    label: 'New Arrival',\n                    className: 'bg-green-50 text-green-700 border-green-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 17\n                    }, this)\n                };\n            default:\n                return {\n                    label: 'Regular Product',\n                    className: 'bg-gray-50 text-gray-700 border-gray-200',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 17\n                    }, this)\n                };\n        }\n    };\n    // Get stock status configuration with functional icons\n    const getStockConfig = (stock)=>{\n        return stock ? {\n            label: 'In Stock',\n            className: 'bg-green-50 text-green-700 border-green-200',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 17\n            }, this)\n        } : {\n            label: 'Out of Stock',\n            className: 'bg-red-50 text-red-700 border-red-200',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this)\n        };\n    };\n    // Handle back navigation\n    const handleBack = ()=>{\n        router.back();\n    };\n    // Handle edit navigation\n    const handleEdit = ()=>{\n        router.push(`/products/edit/${productId}`);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gray-200 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-200 rounded w-48\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-200 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 mt-4\",\n                                                    children: [\n                                                        ...Array(4)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gray-200 rounded-lg\"\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"Error Loading Product\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error || 'Product not found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBack,\n                                className: \"bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors\",\n                                children: \"Go Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare images for gallery\n    const allImages = (0,_lib_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__.getImageUrls)([\n        product.image,\n        ...product.images || []\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleBack,\n                                        className: \"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors\",\n                                        \"aria-label\": \"Go back to products list\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Back to Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-gray-300 hidden sm:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleEdit,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Edit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Edit Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xl:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden sticky top-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ImageGallery__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        images: allImages,\n                                        alt: product.name,\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight mb-2\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Product ID: \",\n                                                                product._id.slice(-8).toUpperCase()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    role: \"group\",\n                                                    \"aria-label\": \"Product status indicators\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${getTypeConfig(product.type).className}`,\n                                                            role: \"status\",\n                                                            \"aria-label\": `Product type: ${getTypeConfig(product.type).label}`,\n                                                            children: [\n                                                                getTypeConfig(product.type).icon,\n                                                                getTypeConfig(product.type).label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${getStockConfig(product.stock).className}`,\n                                                            role: \"status\",\n                                                            \"aria-label\": `Stock status: ${getStockConfig(product.stock).label}`,\n                                                            children: [\n                                                                getStockConfig(product.stock).icon,\n                                                                getStockConfig(product.stock).label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border bg-orange-50 text-orange-700 border-orange-200\",\n                                                            role: \"status\",\n                                                            \"aria-label\": `Discount: ${product.discount}% off`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                product.discount,\n                                                                \"% OFF\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm border-l-4 border-l-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Pricing Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-baseline gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-4xl font-bold text-gray-900\",\n                                                                        \"aria-label\": `Current price: ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)}`,\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl text-gray-500 line-through\",\n                                                                        \"aria-label\": `Original price: ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100))}`,\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 rounded-lg p-3 border border-green-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-700 font-medium flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"You save \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price / (1 - parseFloat(product.discount) / 100) - product.price),\n                                                                        \" (\",\n                                                                        product.discount,\n                                                                        \"% off)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.quantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 bg-gray-50 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price per unit:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(product.price)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Product Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Available Quantity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: [\n                                                                    product.quantity,\n                                                                    \" units\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_CheckCircleIcon_CubeIcon_TagIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Added\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(new Date(product.createdAt))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-6 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEdit,\n                                                    className: \"w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                    children: \"Edit Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                            \"aria-label\": \"Add to favorites\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"hidden lg:inline\",\n                                                                    children: \"Favorite\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                            \"aria-label\": \"Share product\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_HeartIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"hidden lg:inline\",\n                                                                    children: \"Share\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    product.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 lg:p-8 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Product Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed whitespace-pre-wrap\",\n                                        children: product.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\products\\\\view\\\\[id]\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/products/view/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children }) {\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, don't render children (redirect will happen)\n    if (!isAuthenticated) {\n        return null;\n    }\n    // If authenticated, render the protected content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50\",\n            style: {\n                backgroundColor: 'var(--background)'\n            },\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onClose: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col lg:ml-64 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onMenuClick: ()=>setSidebarOpen(true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-3 sm:p-4 md:p-6 overflow-y-auto bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-full xl:max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ onMenuClick }) {\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    const getPageTitle = ()=>{\n        switch(pathname){\n            case '/dashboard':\n                return 'Dashboard';\n            case '/users':\n                return 'User Management';\n            case '/products':\n                return 'Product Management';\n            case '/video-blogs':\n                return 'Video Blog Management';\n            case '/video-blogs/add':\n                return 'Add Video Blog';\n            case '/orders':\n                return 'Order Management';\n            case '/settings':\n                return 'Settings';\n            default:\n                if (pathname.startsWith('/video-blogs/') && pathname.includes('/edit')) {\n                    return 'Edit Video Blog';\n                }\n                return 'CWA Admin Dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-14 sm:h-16 flex items-center px-3 sm:px-4 md:px-6 border-b\",\n        style: {\n            backgroundColor: 'var(--header-background)',\n            boxShadow: 'var(--header-shadow)',\n            borderColor: 'var(--border-color)'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMenuClick,\n                className: \"lg:hidden p-1.5 sm:p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-colors mr-2 sm:mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base sm:text-lg md:text-xl font-semibold truncate\",\n                        style: {\n                            color: 'var(--foreground)'\n                        },\n                        children: getPageTitle()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs sm:text-sm text-slate-500 mt-0.5 hidden sm:block\",\n                        children: \"Welcome back to your admin dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-1.5 sm:p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-0.5 sm:top-1 right-0.5 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3 text-slate-700 hover:text-slate-900 focus:outline-none p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm font-medium\",\n                                            children: user?.name || 'Admin User'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 hidden md:block\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm\",\n                                        children: user?.name ? getInitials(user.name) : 'AU'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 hidden sm:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDZ0Q7QUFDRjtBQUtUO0FBTXRCLFNBQVNLLE9BQU8sRUFBRUMsV0FBVyxFQUFlO0lBQ3pELE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdQLDZEQUFPQTtJQUN4QixNQUFNUSxXQUFXUCw0REFBV0E7SUFFNUIsTUFBTVEsY0FBYyxDQUFDQztRQUNuQixPQUFPQSxLQUNKQyxLQUFLLENBQUMsS0FDTkMsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLENBQUMsSUFDeEJDLElBQUksQ0FBQyxJQUNMQyxXQUFXLEdBQ1hDLEtBQUssQ0FBQyxHQUFHO0lBQ2Q7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLE9BQVFWO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsSUFBSUEsU0FBU1csVUFBVSxDQUFDLG9CQUFvQlgsU0FBU1ksUUFBUSxDQUFDLFVBQVU7b0JBQ3RFLE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsV0FBVTtRQUNWQyxPQUFPO1lBQ0xDLGlCQUFpQjtZQUNqQkMsV0FBVztZQUNYQyxhQUFhO1FBQ2Y7OzBCQUdBLDhEQUFDQztnQkFDQ0MsU0FBU3RCO2dCQUNUZ0IsV0FBVTswQkFFViw0RUFBQ2xCLDRIQUFTQTtvQkFBQ2tCLFdBQVU7Ozs7Ozs7Ozs7OzBCQUd2Qiw4REFBQ087Z0JBQUlQLFdBQVU7O2tDQUNiLDhEQUFDUTt3QkFBR1IsV0FBVTt3QkFBeURDLE9BQU87NEJBQUVRLE9BQU87d0JBQW9CO2tDQUN4R2I7Ozs7OztrQ0FFSCw4REFBQ2M7d0JBQUVWLFdBQVU7a0NBQTJEOzs7Ozs7Ozs7Ozs7MEJBSzFFLDhEQUFDTztnQkFBSVAsV0FBVTs7a0NBRWIsOERBQUNLO3dCQUFPTCxXQUFVOzswQ0FDaEIsOERBQUNwQiw0SEFBUUE7Z0NBQUNvQixXQUFVOzs7Ozs7MENBQ3BCLDhEQUFDVztnQ0FBS1gsV0FBVTs7Ozs7Ozs7Ozs7O2tDQUlsQiw4REFBQ087d0JBQUlQLFdBQVU7a0NBQ2IsNEVBQUNLOzRCQUFPTCxXQUFVOzs4Q0FDaEIsOERBQUNPO29DQUFJUCxXQUFVOztzREFDYiw4REFBQ1U7NENBQUVWLFdBQVU7c0RBQWtDZixNQUFNRyxRQUFROzs7Ozs7c0RBQzdELDhEQUFDc0I7NENBQUVWLFdBQVU7c0RBQXlDOzs7Ozs7Ozs7Ozs7OENBRXhELDhEQUFDTztvQ0FBSVAsV0FBVTs4Q0FDYiw0RUFBQ1c7d0NBQUtYLFdBQVU7a0RBQ2JmLE1BQU1HLE9BQU9ELFlBQVlGLEtBQUtHLElBQUksSUFBSTs7Ozs7Ozs7Ozs7OENBRzNDLDhEQUFDUCw0SEFBZUE7b0NBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU12QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcSGVhZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0L0F1dGhDb250ZXh0JztcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7XG4gIEJlbGxJY29uLFxuICBDaGV2cm9uRG93bkljb24sXG4gIEJhcnMzSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5pbnRlcmZhY2UgSGVhZGVyUHJvcHMge1xuICBvbk1lbnVDbGljazogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVhZGVyKHsgb25NZW51Q2xpY2sgfTogSGVhZGVyUHJvcHMpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcblxuICBjb25zdCBnZXRJbml0aWFscyA9IChuYW1lOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmFtZVxuICAgICAgLnNwbGl0KCcgJylcbiAgICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKSlcbiAgICAgIC5qb2luKCcnKVxuICAgICAgLnRvVXBwZXJDYXNlKClcbiAgICAgIC5zbGljZSgwLCAyKTtcbiAgfTtcblxuICBjb25zdCBnZXRQYWdlVGl0bGUgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChwYXRobmFtZSkge1xuICAgICAgY2FzZSAnL2Rhc2hib2FyZCc6XG4gICAgICAgIHJldHVybiAnRGFzaGJvYXJkJztcbiAgICAgIGNhc2UgJy91c2Vycyc6XG4gICAgICAgIHJldHVybiAnVXNlciBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy9wcm9kdWN0cyc6XG4gICAgICAgIHJldHVybiAnUHJvZHVjdCBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy92aWRlby1ibG9ncyc6XG4gICAgICAgIHJldHVybiAnVmlkZW8gQmxvZyBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy92aWRlby1ibG9ncy9hZGQnOlxuICAgICAgICByZXR1cm4gJ0FkZCBWaWRlbyBCbG9nJztcbiAgICAgIGNhc2UgJy9vcmRlcnMnOlxuICAgICAgICByZXR1cm4gJ09yZGVyIE1hbmFnZW1lbnQnO1xuICAgICAgY2FzZSAnL3NldHRpbmdzJzpcbiAgICAgICAgcmV0dXJuICdTZXR0aW5ncyc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL3ZpZGVvLWJsb2dzLycpICYmIHBhdGhuYW1lLmluY2x1ZGVzKCcvZWRpdCcpKSB7XG4gICAgICAgICAgcmV0dXJuICdFZGl0IFZpZGVvIEJsb2cnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAnQ1dBIEFkbWluIERhc2hib2FyZCc7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlclxuICAgICAgY2xhc3NOYW1lPVwiaC0xNCBzbTpoLTE2IGZsZXggaXRlbXMtY2VudGVyIHB4LTMgc206cHgtNCBtZDpweC02IGJvcmRlci1iXCJcbiAgICAgIHN0eWxlPXt7XG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3ZhcigtLWhlYWRlci1iYWNrZ3JvdW5kKScsXG4gICAgICAgIGJveFNoYWRvdzogJ3ZhcigtLWhlYWRlci1zaGFkb3cpJyxcbiAgICAgICAgYm9yZGVyQ29sb3I6ICd2YXIoLS1ib3JkZXItY29sb3IpJ1xuICAgICAgfX1cbiAgICA+XG4gICAgICB7LyogTW9iaWxlIE1lbnUgQnV0dG9uICovfVxuICAgICAgPGJ1dHRvblxuICAgICAgICBvbkNsaWNrPXtvbk1lbnVDbGlja31cbiAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtMS41IHNtOnAtMiByb3VuZGVkLWxnIHRleHQtc2xhdGUtNjAwIGhvdmVyOnRleHQtc2xhdGUtODAwIGhvdmVyOmJnLXNsYXRlLTEwMCB0cmFuc2l0aW9uLWNvbG9ycyBtci0yIHNtOm1yLTNcIlxuICAgICAgPlxuICAgICAgICA8QmFyczNJY29uIGNsYXNzTmFtZT1cInctNSBoLTUgc206dy02IHNtOmgtNlwiIC8+XG4gICAgICA8L2J1dHRvbj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHNtOnRleHQtbGcgbWQ6dGV4dC14bCBmb250LXNlbWlib2xkIHRydW5jYXRlXCIgc3R5bGU9e3sgY29sb3I6ICd2YXIoLS1mb3JlZ3JvdW5kKScgfX0+XG4gICAgICAgICAge2dldFBhZ2VUaXRsZSgpfVxuICAgICAgICA8L2gyPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1zbGF0ZS01MDAgbXQtMC41IGhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgIFdlbGNvbWUgYmFjayB0byB5b3VyIGFkbWluIGRhc2hib2FyZFxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgc206c3BhY2UteC0yIG1kOnNwYWNlLXgtNFwiPlxuICAgICAgICB7LyogTm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTEuNSBzbTpwLTIgdGV4dC1zbGF0ZS02MDAgaG92ZXI6dGV4dC1zbGF0ZS04MDAgaG92ZXI6Ymctc2xhdGUtMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICA8QmVsbEljb24gY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01XCIgLz5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMC41IHNtOnRvcC0xIHJpZ2h0LTAuNSBzbTpyaWdodC0xIHctMS41IGgtMS41IHNtOnctMiBzbTpoLTIgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgIHsvKiBVc2VyIFByb2ZpbGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzbTpzcGFjZS14LTIgbWQ6c3BhY2UteC0zIHRleHQtc2xhdGUtNzAwIGhvdmVyOnRleHQtc2xhdGUtOTAwIGZvY3VzOm91dGxpbmUtbm9uZSBwLTEuNSBzbTpwLTIgcm91bmRlZC1sZyBob3ZlcjpiZy1zbGF0ZS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBoaWRkZW4gc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3VzZXI/Lm5hbWUgfHwgJ0FkbWluIFVzZXInfTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMCBoaWRkZW4gbWQ6YmxvY2tcIj5BZG1pbmlzdHJhdG9yPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNyBoLTcgc206dy04IHNtOmgtOCBtZDp3LTEwIG1kOmgtMTAgcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1ibHVlLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIHt1c2VyPy5uYW1lID8gZ2V0SW5pdGlhbHModXNlci5uYW1lKSA6ICdBVSd9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zIHNtOnctNCBzbTpoLTQgaGlkZGVuIHNtOmJsb2NrXCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VBdXRoIiwidXNlUGF0aG5hbWUiLCJCZWxsSWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkJhcnMzSWNvbiIsIkhlYWRlciIsIm9uTWVudUNsaWNrIiwidXNlciIsInBhdGhuYW1lIiwiZ2V0SW5pdGlhbHMiLCJuYW1lIiwic3BsaXQiLCJtYXAiLCJ3b3JkIiwiY2hhckF0Iiwiam9pbiIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJnZXRQYWdlVGl0bGUiLCJzdGFydHNXaXRoIiwiaW5jbHVkZXMiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJzdHlsZSIsImJhY2tncm91bmRDb2xvciIsImJveFNoYWRvdyIsImJvcmRlckNvbG9yIiwiYnV0dG9uIiwib25DbGljayIsImRpdiIsImgyIiwiY29sb3IiLCJwIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose }) {\n    const { logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: '/users',\n            label: 'Users',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: '/products',\n            label: 'Products',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: '/video-blogs',\n            label: 'Video Blogs',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: '/orders',\n            label: 'Orders',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    const handleNavClick = ()=>{\n        // Close sidebar on mobile when navigation item is clicked\n        if (window.innerWidth < 1024) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `\n        w-64 h-screen fixed left-0 top-0 z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n        lg:translate-x-0 bg-slate-800 shadow-xl\n      `,\n        style: {\n            backgroundColor: 'var(--sidebar-background)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 lg:p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6 lg:mb-8 lg:block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl lg:text-2xl font-bold text-white\",\n                                    children: \"CWA Admin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs lg:text-sm text-slate-300 mt-1\",\n                                    children: \"Management Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"lg:hidden p-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1 lg:space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: handleNavClick,\n                                    className: `flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg transition-all duration-200 ${isActive ? 'bg-blue-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm lg:text-base\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 lg:mt-8 pt-4 lg:pt-6 border-t border-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 w-full text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm lg:text-base\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                richColors: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHcUQ7QUFDcEI7QUFNbEIsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQzVELHFCQUNFLDhEQUFDSCw4REFBWUE7O1lBQ1ZHOzBCQUNELDhEQUFDRiwyQ0FBT0E7Z0JBQUNHLFVBQVM7Z0JBQVlDLFVBQVU7Ozs7Ozs7Ozs7OztBQUc5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdzb25uZXInO1xyXG5cclxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm92aWRlcnNQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDxUb2FzdGVyIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgcmljaENvbG9ycyAvPlxyXG4gICAgPC9BdXRoUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicG9zaXRpb24iLCJyaWNoQ29sb3JzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ImageGallery.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ImageGallery.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageGallery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageGallery({ images, alt, className = '' }) {\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLightboxOpen, setIsLightboxOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(images.length).fill(true));\n    // Handle keyboard navigation\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageGallery.useCallback[handleKeyDown]\": (e)=>{\n            if (!isLightboxOpen) return;\n            switch(e.key){\n                case 'Escape':\n                    setIsLightboxOpen(false);\n                    break;\n                case 'ArrowLeft':\n                    setSelectedIndex({\n                        \"ImageGallery.useCallback[handleKeyDown]\": (prev)=>prev > 0 ? prev - 1 : images.length - 1\n                    }[\"ImageGallery.useCallback[handleKeyDown]\"]);\n                    break;\n                case 'ArrowRight':\n                    setSelectedIndex({\n                        \"ImageGallery.useCallback[handleKeyDown]\": (prev)=>prev < images.length - 1 ? prev + 1 : 0\n                    }[\"ImageGallery.useCallback[handleKeyDown]\"]);\n                    break;\n            }\n        }\n    }[\"ImageGallery.useCallback[handleKeyDown]\"], [\n        isLightboxOpen,\n        images.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGallery.useEffect\": ()=>{\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ImageGallery.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ImageGallery.useEffect\"];\n        }\n    }[\"ImageGallery.useEffect\"], [\n        handleKeyDown\n    ]);\n    // Prevent body scroll when lightbox is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGallery.useEffect\": ()=>{\n            if (isLightboxOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"ImageGallery.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"ImageGallery.useEffect\"];\n        }\n    }[\"ImageGallery.useEffect\"], [\n        isLightboxOpen\n    ]);\n    const handleImageLoad = (index)=>{\n        setIsLoading((prev)=>{\n            const newLoading = [\n                ...prev\n            ];\n            newLoading[index] = false;\n            return newLoading;\n        });\n    };\n    const handleImageError = (e, index)=>{\n        const target = e.target;\n        target.src = '/placeholder-product.svg';\n        handleImageLoad(index);\n    };\n    const openLightbox = (index)=>{\n        setSelectedIndex(index);\n        setIsLightboxOpen(true);\n    };\n    const navigateImage = (direction)=>{\n        if (direction === 'prev') {\n            setSelectedIndex((prev)=>prev > 0 ? prev - 1 : images.length - 1);\n        } else {\n            setSelectedIndex((prev)=>prev < images.length - 1 ? prev + 1 : 0);\n        }\n    };\n    if (!images || images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `aspect-square bg-gray-100 rounded-xl flex items-center justify-center ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-2 opacity-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"No images available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `space-y-4 ${className}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative aspect-square bg-gray-100 rounded-xl overflow-hidden group\",\n                        children: [\n                            isLoading[selectedIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: images[selectedIndex],\n                                alt: `${alt} - Image ${selectedIndex + 1}`,\n                                className: \"w-full h-full object-cover transition-opacity duration-300\",\n                                onLoad: ()=>handleImageLoad(selectedIndex),\n                                onError: (e)=>handleImageError(e, selectedIndex)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>openLightbox(selectedIndex),\n                                className: \"absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                \"aria-label\": \"Open image in lightbox\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-full p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateImage('prev'),\n                                        className: \"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                        \"aria-label\": \"Previous image\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateImage('next'),\n                                        className: \"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                        \"aria-label\": \"Next image\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full\",\n                                children: [\n                                    selectedIndex + 1,\n                                    \" / \",\n                                    images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 overflow-x-auto pb-2\",\n                        children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedIndex(index),\n                                className: `flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${selectedIndex === index ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'}`,\n                                \"aria-label\": `View image ${index + 1}`,\n                                children: [\n                                    isLoading[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full flex items-center justify-center bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: `${alt} thumbnail ${index + 1}`,\n                                        className: \"w-full h-full object-cover\",\n                                        onLoad: ()=>handleImageLoad(index),\n                                        onError: (e)=>handleImageError(e, index)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            isLightboxOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4\",\n                role: \"dialog\",\n                \"aria-modal\": \"true\",\n                \"aria-labelledby\": \"lightbox-title\",\n                \"aria-describedby\": \"lightbox-description\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sr-only\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                id: \"lightbox-title\",\n                                children: \"Image Gallery Lightbox\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                id: \"lightbox-description\",\n                                children: [\n                                    \"Viewing image \",\n                                    selectedIndex + 1,\n                                    \" of \",\n                                    images.length,\n                                    \". Use arrow keys to navigate, escape to close.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsLightboxOpen(false),\n                        className: \"absolute top-4 right-4 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black\",\n                        \"aria-label\": \"Close lightbox (Escape key)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6 sm:w-8 sm:h-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>navigateImage('prev'),\n                                className: \"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black\",\n                                \"aria-label\": `Previous image (${selectedIndex} of ${images.length})`,\n                                disabled: selectedIndex === 0,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6 sm:w-8 sm:h-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>navigateImage('next'),\n                                className: \"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black\",\n                                \"aria-label\": `Next image (${selectedIndex + 2} of ${images.length})`,\n                                disabled: selectedIndex === images.length - 1,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 sm:w-8 sm:h-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl max-h-full\",\n                        role: \"img\",\n                        \"aria-label\": `${alt} - Image ${selectedIndex + 1} of ${images.length}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: images[selectedIndex],\n                            alt: `${alt} - Image ${selectedIndex + 1} of ${images.length}`,\n                            className: \"max-w-full max-h-full object-contain\",\n                            onError: (e)=>handleImageError(e, selectedIndex)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 left-1/2 -translate-x-1/2 text-white text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm opacity-75\",\n                            \"aria-live\": \"polite\",\n                            children: [\n                                \"Image \",\n                                selectedIndex + 1,\n                                \" of \",\n                                images.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\ui\\\\ImageGallery.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ImageGallery.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/apiClient */ \"(ssr)/./src/lib/api/apiClient.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null\n};\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        const userId = localStorage.getItem('userId');\n                        const name = localStorage.getItem('name');\n                        const email = localStorage.getItem('email');\n                        const phone = localStorage.getItem('phone');\n                        const isAdmin = localStorage.getItem('isAdmin');\n                        const createdAt = localStorage.getItem('createdAt');\n                        if (token && userId) {\n                            setAuthState({\n                                user: {\n                                    id: userId,\n                                    name: name || 'User',\n                                    email: email || '',\n                                    phone: phone || '',\n                                    isAdmin: isAdmin === 'true',\n                                    createdAt: createdAt || new Date().toISOString()\n                                },\n                                isAuthenticated: true,\n                                isLoading: false,\n                                error: null\n                            });\n                        } else {\n                            setAuthState({\n                                ...initialState,\n                                isLoading: false\n                            });\n                        }\n                    } catch (error) {\n                        console.log(error);\n                        setAuthState({\n                            ...initialState,\n                            isLoading: false\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post('/auth/login', {\n                email,\n                password\n            });\n            console.log(\"Login response:\", response.data);\n            if (response.data.status === 'success') {\n                const { user, token } = response.data.data;\n                // Store auth data in localStorage\n                localStorage.setItem('token', token);\n                localStorage.setItem('userId', user.id);\n                localStorage.setItem('name', user.name);\n                localStorage.setItem('email', user.email);\n                localStorage.setItem('phone', user.phone || '');\n                localStorage.setItem('isAdmin', user.isAdmin ? 'true' : 'false');\n                localStorage.setItem('createdAt', user.createdAt || new Date().toISOString());\n                // Update auth state\n                setAuthState({\n                    user: {\n                        id: user.id,\n                        name: user.name,\n                        email: user.email,\n                        phone: user.phone || '',\n                        isAdmin: user.isAdmin,\n                        createdAt: user.createdAt || new Date().toISOString()\n                    },\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged in successfully!');\n                // Check if user is admin\n                // if (user.isAdmin) {\n                router.push('/dashboard');\n            // } else {\n            //   throw new Error('Access denied. Admin privileges required.');\n            // }\n            } else {\n                throw new Error('Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Invalid email or password';\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n        }\n    };\n    // Logout function\n    const logout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('name');\n        localStorage.removeItem('email');\n        localStorage.removeItem('phone');\n        localStorage.removeItem('isAdmin');\n        localStorage.removeItem('createdAt');\n        // Reset auth state\n        setAuthState(initialState);\n        // Redirect to login\n        router.push('/auth/login');\n        sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged out successfully!');\n    };\n    // Function to directly set auth state (used by auth-callback)\n    const setAuthStateDirectly = (state)=>{\n        setAuthState(state);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            logout,\n            setAuthState: setAuthStateDirectly\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU9jO0FBQzhCO0FBQ2hCO0FBRWdCO0FBRTVDLE1BQU1RLGVBQXVCO0lBQ3pCQyxNQUFLO0lBQ0xDLGlCQUFnQjtJQUNoQkMsV0FBVTtJQUNWQyxPQUFNO0FBQ1Y7QUFDQSxNQUFNQyw0QkFBWVosb0RBQWFBLENBQTRCYTtBQUNwRCxNQUFNQyxlQUFhLENBQUMsRUFBRUMsUUFBUSxFQUEyQjtJQUM1RCxNQUFNLENBQUNDLFdBQVVDLGFBQWEsR0FBQ2hCLCtDQUFRQSxDQUFZTTtJQUNuRCxNQUFNVyxTQUFPZCwwREFBU0E7SUFFdEJGLGdEQUFTQTtrQ0FBQztZQUNOLE1BQU1pQjtvREFBWTtvQkFDZCxJQUFJO3dCQUNBLE1BQU1DLFFBQU1DLGFBQWFDLE9BQU8sQ0FBQzt3QkFDckMsTUFBTUMsU0FBT0YsYUFBYUMsT0FBTyxDQUFDO3dCQUNsQyxNQUFNRSxPQUFLSCxhQUFhQyxPQUFPLENBQUM7d0JBQ2hDLE1BQU1HLFFBQU1KLGFBQWFDLE9BQU8sQ0FBQzt3QkFDakMsTUFBTUksUUFBTUwsYUFBYUMsT0FBTyxDQUFDO3dCQUNqQyxNQUFNSyxVQUFRTixhQUFhQyxPQUFPLENBQUM7d0JBQ25DLE1BQU1NLFlBQVVQLGFBQWFDLE9BQU8sQ0FBQzt3QkFDckMsSUFBR0YsU0FBU0csUUFBTzs0QkFDZk4sYUFDSTtnQ0FDSVQsTUFBSztvQ0FDRHFCLElBQUdOO29DQUNIQyxNQUFLQSxRQUFPO29DQUNaQyxPQUFNQSxTQUFRO29DQUNkQyxPQUFNQSxTQUFRO29DQUNkQyxTQUFRQSxZQUFZO29DQUNwQkMsV0FBVUEsYUFBYSxJQUFJRSxPQUFPQyxXQUFXO2dDQUNqRDtnQ0FDQXRCLGlCQUFnQjtnQ0FDaEJDLFdBQVU7Z0NBQ1ZDLE9BQU07NEJBRVY7d0JBRVIsT0FDSTs0QkFDQU0sYUFBYTtnQ0FDVCxHQUFHVixZQUFZO2dDQUNmRyxXQUFVOzRCQUNkO3dCQUNKO29CQUNKLEVBQ0gsT0FBT0MsT0FBTzt3QkFDSHFCLFFBQVFDLEdBQUcsQ0FBQ3RCO3dCQUNaTSxhQUFhOzRCQUNULEdBQUdWLFlBQVk7NEJBQ2ZHLFdBQVU7d0JBQ2Q7b0JBQ0o7Z0JBQ0o7O1lBQ0FTO1FBQ0E7aUNBQUUsRUFBRTtJQUNKLE1BQU1lLFFBQU0sT0FBUVQsT0FBYVU7UUFDakNsQixhQUFhLENBQUNtQixPQUFRO2dCQUNsQixHQUFHQSxJQUFJO2dCQUFDMUIsV0FBVTtnQkFBS0MsT0FBTTtZQUNqQztRQUNBLElBQUk7WUFDQSxNQUFNMEIsV0FBUyxNQUFNL0IsMERBQVNBLENBQUNnQyxJQUFJLENBQUMsZUFBYztnQkFDOUNiO2dCQUFNVTtZQUNWO1lBQ0FILFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJJLFNBQVNFLElBQUk7WUFDaEQsSUFBSUYsU0FBU0UsSUFBSSxDQUFDQyxNQUFNLEtBQUssV0FBVztnQkFDeEMsTUFBTSxFQUFFaEMsSUFBSSxFQUFFWSxLQUFLLEVBQUUsR0FBR2lCLFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFFMUMsa0NBQWtDO2dCQUNsQ2xCLGFBQWFvQixPQUFPLENBQUMsU0FBU3JCO2dCQUM5QkMsYUFBYW9CLE9BQU8sQ0FBQyxVQUFVakMsS0FBS3FCLEVBQUU7Z0JBQ3RDUixhQUFhb0IsT0FBTyxDQUFDLFFBQVFqQyxLQUFLZ0IsSUFBSTtnQkFDdENILGFBQWFvQixPQUFPLENBQUMsU0FBU2pDLEtBQUtpQixLQUFLO2dCQUN4Q0osYUFBYW9CLE9BQU8sQ0FBQyxTQUFTakMsS0FBS2tCLEtBQUssSUFBSTtnQkFDNUNMLGFBQWFvQixPQUFPLENBQUMsV0FBV2pDLEtBQUttQixPQUFPLEdBQUcsU0FBUztnQkFDeEROLGFBQWFvQixPQUFPLENBQUMsYUFBYWpDLEtBQUtvQixTQUFTLElBQUksSUFBSUUsT0FBT0MsV0FBVztnQkFFMUUsb0JBQW9CO2dCQUNwQmQsYUFBYTtvQkFDWFQsTUFBTTt3QkFDSnFCLElBQUlyQixLQUFLcUIsRUFBRTt3QkFDWEwsTUFBTWhCLEtBQUtnQixJQUFJO3dCQUNmQyxPQUFPakIsS0FBS2lCLEtBQUs7d0JBQ2pCQyxPQUFPbEIsS0FBS2tCLEtBQUssSUFBSTt3QkFDckJDLFNBQVNuQixLQUFLbUIsT0FBTzt3QkFDckJDLFdBQVdwQixLQUFLb0IsU0FBUyxJQUFJLElBQUlFLE9BQU9DLFdBQVc7b0JBQ3JEO29CQUNBdEIsaUJBQWlCO29CQUNqQkMsV0FBVztvQkFDWEMsT0FBTztnQkFDVDtnQkFFQU4seUNBQUtBLENBQUNxQyxPQUFPLENBQUM7Z0JBRWQseUJBQXlCO2dCQUN6QixzQkFBc0I7Z0JBQ3BCeEIsT0FBT3lCLElBQUksQ0FBQztZQUNkLFdBQVc7WUFDWCxrRUFBa0U7WUFDbEUsSUFBSTtZQUNOLE9BQU87Z0JBQ0wsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPakMsT0FBZ0I7WUFDdkJxQixRQUFRckIsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsTUFBTWtDLGVBQWVsQyxpQkFBaUJpQyxRQUFRakMsTUFBTW1DLE9BQU8sR0FBRztZQUM5RDdCLGFBQWEsQ0FBQ21CLE9BQVU7b0JBQ3RCLEdBQUdBLElBQUk7b0JBQ1AxQixXQUFXO29CQUNYQyxPQUFPa0M7Z0JBQ1Q7WUFDQXhDLHlDQUFLQSxDQUFDTSxLQUFLLENBQUNrQztRQUNkO0lBQ0Y7SUFDQSxrQkFBa0I7SUFDbEIsTUFBTUUsU0FBUztRQUNiLHFCQUFxQjtRQUNyQjFCLGFBQWEyQixVQUFVLENBQUM7UUFDeEIzQixhQUFhMkIsVUFBVSxDQUFDO1FBQ3hCM0IsYUFBYTJCLFVBQVUsQ0FBQztRQUN4QjNCLGFBQWEyQixVQUFVLENBQUM7UUFDeEIzQixhQUFhMkIsVUFBVSxDQUFDO1FBQ3hCM0IsYUFBYTJCLFVBQVUsQ0FBQztRQUN4QjNCLGFBQWEyQixVQUFVLENBQUM7UUFFeEIsbUJBQW1CO1FBQ25CL0IsYUFBYVY7UUFFYixvQkFBb0I7UUFDcEJXLE9BQU95QixJQUFJLENBQUM7UUFFWnRDLHlDQUFLQSxDQUFDcUMsT0FBTyxDQUFDO0lBQ2hCO0lBRUEsOERBQThEO0lBQzlELE1BQU1PLHVCQUF1QixDQUFDQztRQUM1QmpDLGFBQWFpQztJQUNmO0lBRUEscUJBQ0UsOERBQUN0QyxZQUFZdUMsUUFBUTtRQUNuQkMsT0FBTztZQUNMLEdBQUdwQyxTQUFTO1lBQ1prQjtZQUNBYTtZQUNBOUIsY0FBY2dDO1FBQ2hCO2tCQUVDbEM7Ozs7OztBQUdQLEVBQUU7QUFDRixrQ0FBa0M7QUFDM0IsTUFBTXNDLFVBQVU7SUFDckIsTUFBTUMsVUFBVW5ELGlEQUFVQSxDQUFDUztJQUMzQixJQUFJMEMsWUFBWXpDLFdBQVc7UUFDekIsTUFBTSxJQUFJK0IsTUFBTTtJQUNsQjtJQUNBLE9BQU9VO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbnRleHRcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcbmltcG9ydCBSZWFjdCAse1xyXG4gICAgY3JlYXRlQ29udGV4dCxcclxuICAgIHVzZVN0YXRlLFxyXG4gICAgdXNlRWZmZWN0LFxyXG4gICAgUmVhY3ROb2RlLFxyXG4gICAgdXNlQ29udGV4dCxcclxufSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHt0b2FzdH0gZnJvbSAnc29ubmVyJ1xyXG5pbXBvcnQge0F1dGhTdGF0ZSwgQXV0aENvbnRleHRUeXBlfSBmcm9tICcuLi90eXBlcy91c2VyJztcclxuaW1wb3J0IGFwaUNsaWVudCBmcm9tICdAL2xpYi9hcGkvYXBpQ2xpZW50JztcclxuXHJcbmNvbnN0IGluaXRpYWxTdGF0ZTpBdXRoU3RhdGU9e1xyXG4gICAgdXNlcjpudWxsLFxyXG4gICAgaXNBdXRoZW50aWNhdGVkOmZhbHNlLFxyXG4gICAgaXNMb2FkaW5nOnRydWUsXHJcbiAgICBlcnJvcjpudWxsXHJcbn1cclxuY29uc3QgQXV0aENvbnRleHQ9Y3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGV8dW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyPSh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSA9PntcclxuICAgIGNvbnN0IFthdXRoU3RhdGUsc2V0QXV0aFN0YXRlXT11c2VTdGF0ZTxBdXRoU3RhdGU+KGluaXRpYWxTdGF0ZSlcclxuICAgIGNvbnN0IHJvdXRlcj11c2VSb3V0ZXIoKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKT0+e1xyXG4gICAgICAgIGNvbnN0IGNoZWNrQXV0aCA9ICgpPT57XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0b2tlbj1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxyXG4gICAgICAgICAgICBjb25zdCB1c2VySWQ9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpO1xyXG4gICAgICAgICAgICBjb25zdCBuYW1lPWxvY2FsU3RvcmFnZS5nZXRJdGVtKCduYW1lJylcclxuICAgICAgICAgICAgY29uc3QgZW1haWw9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2VtYWlsJylcclxuICAgICAgICAgICAgY29uc3QgcGhvbmU9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Bob25lJylcclxuICAgICAgICAgICAgY29uc3QgaXNBZG1pbj1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaXNBZG1pbicpXHJcbiAgICAgICAgICAgIGNvbnN0IGNyZWF0ZWRBdD1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3JlYXRlZEF0JylcclxuICAgICAgICAgICAgaWYodG9rZW4gJiYgdXNlcklkKXtcclxuICAgICAgICAgICAgICAgIHNldEF1dGhTdGF0ZShcclxuICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXI6e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6dXNlcklkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTpuYW1lIHx8J1VzZXInLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6ZW1haWwgfHwnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBob25lOnBob25lIHx8JycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0FkbWluOmlzQWRtaW4gPT09ICd0cnVlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZWRBdDpjcmVhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZDp0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc6ZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yOm51bGxcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVsc2V7XHJcbiAgICAgICAgICAgICAgICBzZXRBdXRoU3RhdGUoe1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLmluaXRpYWxTdGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc6ZmFsc2VcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnJvcilcclxuICAgICAgICAgICAgICAgIHNldEF1dGhTdGF0ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uaW5pdGlhbFN0YXRlLFxyXG4gICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZzpmYWxzZVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBjaGVja0F1dGgoKVxyXG4gICAgICAgIH0sW10pO1xyXG4gICAgICAgIGNvbnN0IGxvZ2luPWFzeW5jICAoZW1haWw6c3RyaW5nLHBhc3N3b3JkOnN0cmluZyk9PntcclxuICAgICAgICBzZXRBdXRoU3RhdGUoKHByZXYpPT4oe1xyXG4gICAgICAgICAgICAuLi5wcmV2LGlzTG9hZGluZzp0cnVlLGVycm9yOm51bGxcclxuICAgICAgICB9KSlcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZT1hd2FpdCBhcGlDbGllbnQucG9zdCgnL2F1dGgvbG9naW4nLHtcclxuICAgICAgICAgICAgICAgIGVtYWlsLHBhc3N3b3JkXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiTG9naW4gcmVzcG9uc2U6XCIsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgY29uc3QgeyB1c2VyLCB0b2tlbiB9ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xyXG5cclxuICAgICAgICAvLyBTdG9yZSBhdXRoIGRhdGEgaW4gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Rva2VuJywgdG9rZW4pO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VySWQnLCB1c2VyLmlkKTtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbmFtZScsIHVzZXIubmFtZSk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2VtYWlsJywgdXNlci5lbWFpbCk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Bob25lJywgdXNlci5waG9uZSB8fCAnJyk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2lzQWRtaW4nLCB1c2VyLmlzQWRtaW4gPyAndHJ1ZScgOiAnZmFsc2UnKTtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY3JlYXRlZEF0JywgdXNlci5jcmVhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpKTtcclxuXHJcbiAgICAgICAgLy8gVXBkYXRlIGF1dGggc3RhdGVcclxuICAgICAgICBzZXRBdXRoU3RhdGUoe1xyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogdXNlci5pZCxcclxuICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgICAgcGhvbmU6IHVzZXIucGhvbmUgfHwgJycsXHJcbiAgICAgICAgICAgIGlzQWRtaW46IHVzZXIuaXNBZG1pbixcclxuICAgICAgICAgICAgY3JlYXRlZEF0OiB1c2VyLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxyXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcclxuICAgICAgICAgIGVycm9yOiBudWxsLFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICB0b2FzdC5zdWNjZXNzKCdMb2dnZWQgaW4gc3VjY2Vzc2Z1bGx5IScpO1xyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFkbWluXHJcbiAgICAgICAgLy8gaWYgKHVzZXIuaXNBZG1pbikge1xyXG4gICAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcclxuICAgICAgICAvLyB9IGVsc2Uge1xyXG4gICAgICAgIC8vICAgdGhyb3cgbmV3IEVycm9yKCdBY2Nlc3MgZGVuaWVkLiBBZG1pbiBwcml2aWxlZ2VzIHJlcXVpcmVkLicpO1xyXG4gICAgICAgIC8vIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvZ2luIGZhaWxlZCcpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogdW5rbm93bikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ludmFsaWQgZW1haWwgb3IgcGFzc3dvcmQnO1xyXG4gICAgICBzZXRBdXRoU3RhdGUoKHByZXYpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UsXHJcbiAgICAgIH0pKTtcclxuICAgICAgdG9hc3QuZXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgIH1cclxuICB9O1xyXG4gIC8vIExvZ291dCBmdW5jdGlvblxyXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcclxuICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZVxyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcklkJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnbmFtZScpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2VtYWlsJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncGhvbmUnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdpc0FkbWluJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY3JlYXRlZEF0Jyk7XHJcblxyXG4gICAgLy8gUmVzZXQgYXV0aCBzdGF0ZVxyXG4gICAgc2V0QXV0aFN0YXRlKGluaXRpYWxTdGF0ZSk7XHJcblxyXG4gICAgLy8gUmVkaXJlY3QgdG8gbG9naW5cclxuICAgIHJvdXRlci5wdXNoKCcvYXV0aC9sb2dpbicpO1xyXG5cclxuICAgIHRvYXN0LnN1Y2Nlc3MoJ0xvZ2dlZCBvdXQgc3VjY2Vzc2Z1bGx5IScpO1xyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIGRpcmVjdGx5IHNldCBhdXRoIHN0YXRlICh1c2VkIGJ5IGF1dGgtY2FsbGJhY2spXHJcbiAgY29uc3Qgc2V0QXV0aFN0YXRlRGlyZWN0bHkgPSAoc3RhdGU6IEF1dGhTdGF0ZSkgPT4ge1xyXG4gICAgc2V0QXV0aFN0YXRlKHN0YXRlKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyXHJcbiAgICAgIHZhbHVlPXt7XHJcbiAgICAgICAgLi4uYXV0aFN0YXRlLFxyXG4gICAgICAgIGxvZ2luLFxyXG4gICAgICAgIGxvZ291dCxcclxuICAgICAgICBzZXRBdXRoU3RhdGU6IHNldEF1dGhTdGF0ZURpcmVjdGx5LFxyXG4gICAgICB9fVxyXG4gICAgPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn07XHJcbi8vIEN1c3RvbSBob29rIHRvIHVzZSBhdXRoIGNvbnRleHRcclxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKTogQXV0aENvbnRleHRUeXBlID0+IHtcclxuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XHJcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59O1xyXG5cclxuXHJcblxyXG5cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ29udGV4dCIsInVzZVJvdXRlciIsInRvYXN0IiwiYXBpQ2xpZW50IiwiaW5pdGlhbFN0YXRlIiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsImVycm9yIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImF1dGhTdGF0ZSIsInNldEF1dGhTdGF0ZSIsInJvdXRlciIsImNoZWNrQXV0aCIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXJJZCIsIm5hbWUiLCJlbWFpbCIsInBob25lIiwiaXNBZG1pbiIsImNyZWF0ZWRBdCIsImlkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY29uc29sZSIsImxvZyIsImxvZ2luIiwicGFzc3dvcmQiLCJwcmV2IiwicmVzcG9uc2UiLCJwb3N0IiwiZGF0YSIsInN0YXR1cyIsInNldEl0ZW0iLCJzdWNjZXNzIiwicHVzaCIsIkVycm9yIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsImxvZ291dCIsInJlbW92ZUl0ZW0iLCJzZXRBdXRoU3RhdGVEaXJlY3RseSIsInN0YXRlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUF1dGgiLCJjb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/apiClient.ts":
/*!**********************************!*\
  !*** ./src/lib/api/apiClient.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/productService.ts":
/*!***************************************!*\
  !*** ./src/lib/api/productService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n\nclass ProductService {\n    /**\n   * Get all products with optional filters\n   */ async getProducts(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            // Add filters to query params\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.category) {\n                params.append('category', filters.category);\n            }\n            if (filters.type) {\n                params.append('type', filters.type);\n            }\n            if (filters.minPrice !== undefined) {\n                params.append('price[gte]', filters.minPrice.toString());\n            }\n            if (filters.maxPrice !== undefined) {\n                params.append('price[lte]', filters.maxPrice.toString());\n            }\n            if (filters.inStock !== undefined) {\n                params.append('stock', filters.inStock.toString());\n            }\n            if (filters.sort) {\n                params.append('sort', filters.sort);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `/products${queryString ? `?${queryString}` : ''}`;\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get a single product by ID\n   */ async getProduct(id) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/products/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new product with FormData (for file uploads)\n   */ async createProduct(formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            // Use Next.js API proxy to avoid CORS issues\n            const response = await fetch('/api/products', {\n                method: 'POST',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to create product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error creating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update a product with FormData (for file uploads)\n   */ async updateProduct(id, formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'PATCH',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to update product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error updating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a product\n   */ async deleteProduct(id) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for consistent authorization handling\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Product deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get product categories\n   */ async getCategories() {\n        try {\n            const response = await this.getProducts();\n            const categories = [\n                ...new Set(response.data.products.map((product)=>product.category))\n            ];\n            return categories.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            return [];\n        }\n    }\n}\nconst productService = new ProductService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (productService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9wcm9kdWN0U2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQWVuRCxNQUFNQztJQUNKOztHQUVDLEdBQ0QsTUFBTUMsWUFBWUMsVUFBMEIsQ0FBQyxDQUFDLEVBQTZCO1FBQ3pFLElBQUk7WUFDRixNQUFNQyxTQUFTLElBQUlDO1lBRW5CLDhCQUE4QjtZQUM5QixJQUFJRixRQUFRRyxNQUFNLEVBQUU7Z0JBQ2xCRixPQUFPRyxNQUFNLENBQUMsVUFBVUosUUFBUUcsTUFBTTtZQUN4QztZQUNBLElBQUlILFFBQVFLLFFBQVEsRUFBRTtnQkFDcEJKLE9BQU9HLE1BQU0sQ0FBQyxZQUFZSixRQUFRSyxRQUFRO1lBQzVDO1lBQ0EsSUFBSUwsUUFBUU0sSUFBSSxFQUFFO2dCQUNoQkwsT0FBT0csTUFBTSxDQUFDLFFBQVFKLFFBQVFNLElBQUk7WUFDcEM7WUFDQSxJQUFJTixRQUFRTyxRQUFRLEtBQUtDLFdBQVc7Z0JBQ2xDUCxPQUFPRyxNQUFNLENBQUMsY0FBY0osUUFBUU8sUUFBUSxDQUFDRSxRQUFRO1lBQ3ZEO1lBQ0EsSUFBSVQsUUFBUVUsUUFBUSxLQUFLRixXQUFXO2dCQUNsQ1AsT0FBT0csTUFBTSxDQUFDLGNBQWNKLFFBQVFVLFFBQVEsQ0FBQ0QsUUFBUTtZQUN2RDtZQUNBLElBQUlULFFBQVFXLE9BQU8sS0FBS0gsV0FBVztnQkFDakNQLE9BQU9HLE1BQU0sQ0FBQyxTQUFTSixRQUFRVyxPQUFPLENBQUNGLFFBQVE7WUFDakQ7WUFDQSxJQUFJVCxRQUFRWSxJQUFJLEVBQUU7Z0JBQ2hCWCxPQUFPRyxNQUFNLENBQUMsUUFBUUosUUFBUVksSUFBSTtZQUNwQztZQUNBLElBQUlaLFFBQVFhLElBQUksRUFBRTtnQkFDaEJaLE9BQU9HLE1BQU0sQ0FBQyxRQUFRSixRQUFRYSxJQUFJLENBQUNKLFFBQVE7WUFDN0M7WUFDQSxJQUFJVCxRQUFRYyxLQUFLLEVBQUU7Z0JBQ2pCYixPQUFPRyxNQUFNLENBQUMsU0FBU0osUUFBUWMsS0FBSyxDQUFDTCxRQUFRO1lBQy9DO1lBRUEsTUFBTU0sY0FBY2QsT0FBT1EsUUFBUTtZQUNuQyxNQUFNTyxNQUFNLENBQUMsU0FBUyxFQUFFRCxjQUFjLENBQUMsQ0FBQyxFQUFFQSxhQUFhLEdBQUcsSUFBSTtZQUU5RCxNQUFNRSxXQUFXLE1BQU1wQiwwREFBU0EsQ0FBQ3FCLEdBQUcsQ0FBbUJGO1lBQ3ZELE9BQU9DLFNBQVNFLElBQUk7UUFDdEIsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUUsV0FBV0MsRUFBVSxFQUE4QztRQUN2RSxJQUFJO1lBQ0YsTUFBTU4sV0FBVyxNQUFNcEIsMERBQVNBLENBQUNxQixHQUFHLENBQW9DLENBQUMsVUFBVSxFQUFFSyxJQUFJO1lBQ3pGLE9BQU9OLFNBQVNFLElBQUk7UUFDdEIsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUksY0FBY0MsUUFBa0IsRUFBMkQ7UUFDL0YsSUFBSTtZQUNGLGlEQUFpRDtZQUNqRCxNQUFNQyxRQUFRLE1BQTZCLEdBQUdDLENBQTZCLEdBQUc7WUFFOUUsTUFBTUUsVUFBdUIsQ0FBQztZQUM5QixJQUFJSCxPQUFPO2dCQUNURyxPQUFPLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxPQUFPLEVBQUVILE9BQU87WUFDOUM7WUFFQSxxRUFBcUU7WUFDckUsNkNBQTZDO1lBQzdDLE1BQU1ULFdBQVcsTUFBTWEsTUFBTSxpQkFBaUI7Z0JBQzVDQyxRQUFRO2dCQUNSRjtnQkFDQUcsTUFBTVA7WUFDUjtZQUVBLElBQUksQ0FBQ1IsU0FBU2dCLEVBQUUsRUFBRTtnQkFDaEIsTUFBTUMsWUFBWSxNQUFNakIsU0FBU2tCLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU87d0JBQUVDLFNBQVM7b0JBQTJCO2dCQUMzRixNQUFNLElBQUlDLE1BQU1KLFVBQVVHLE9BQU8sSUFBSSxDQUFDLG9CQUFvQixFQUFFcEIsU0FBU3NCLE1BQU0sRUFBRTtZQUMvRTtZQUVBLE9BQU8sTUFBTXRCLFNBQVNrQixJQUFJO1FBQzVCLEVBQUUsT0FBT2YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1vQixjQUFjakIsRUFBVSxFQUFFRSxRQUFrQixFQUEyRDtRQUMzRyxJQUFJO1lBQ0YsaURBQWlEO1lBQ2pELE1BQU1DLFFBQVEsTUFBNkIsR0FBR0MsQ0FBNkIsR0FBRztZQUU5RSxNQUFNRSxVQUF1QixDQUFDO1lBQzlCLElBQUlILE9BQU87Z0JBQ1RHLE9BQU8sQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLE9BQU8sRUFBRUgsT0FBTztZQUM5QztZQUVBLHFFQUFxRTtZQUNyRSxNQUFNVCxXQUFXLE1BQU1hLE1BQU0sQ0FBQyxjQUFjLEVBQUVQLElBQUksRUFBRTtnQkFDbERRLFFBQVE7Z0JBQ1JGO2dCQUNBRyxNQUFNUDtZQUNSO1lBRUEsSUFBSSxDQUFDUixTQUFTZ0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1qQixTQUFTa0IsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTzt3QkFBRUMsU0FBUztvQkFBMkI7Z0JBQzNGLE1BQU0sSUFBSUMsTUFBTUosVUFBVUcsT0FBTyxJQUFJLENBQUMsb0JBQW9CLEVBQUVwQixTQUFTc0IsTUFBTSxFQUFFO1lBQy9FO1lBRUEsT0FBTyxNQUFNdEIsU0FBU2tCLElBQUk7UUFDNUIsRUFBRSxPQUFPZixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXFCLGNBQWNsQixFQUFVLEVBQWdEO1FBQzVFLElBQUk7WUFDRixpREFBaUQ7WUFDakQsTUFBTUcsUUFBUSxNQUE2QixHQUFHQyxDQUE2QixHQUFHO1lBRTlFLE1BQU1FLFVBQXVCO2dCQUMzQixnQkFBZ0I7WUFDbEI7WUFFQSxJQUFJSCxPQUFPO2dCQUNURyxPQUFPLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxPQUFPLEVBQUVILE9BQU87WUFDOUM7WUFFQSwyREFBMkQ7WUFDM0QsTUFBTVQsV0FBVyxNQUFNYSxNQUFNLENBQUMsY0FBYyxFQUFFUCxJQUFJLEVBQUU7Z0JBQ2xEUSxRQUFRO2dCQUNSRjtZQUNGO1lBRUEsSUFBSSxDQUFDWixTQUFTZ0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1qQixTQUFTa0IsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTzt3QkFBRUMsU0FBUztvQkFBMkI7Z0JBQzNGLE1BQU0sSUFBSUMsTUFBTUosVUFBVUcsT0FBTyxJQUFJLENBQUMsb0JBQW9CLEVBQUVwQixTQUFTc0IsTUFBTSxFQUFFO1lBQy9FO1lBRUEsaUNBQWlDO1lBQ2pDLElBQUl0QixTQUFTc0IsTUFBTSxLQUFLLEtBQUs7Z0JBQzNCLE9BQU87b0JBQUVBLFFBQVE7b0JBQVdGLFNBQVM7Z0JBQStCO1lBQ3RFO1lBRUEsTUFBTWxCLE9BQU8sTUFBTUYsU0FBU2tCLElBQUk7WUFDaEMsT0FBT2hCO1FBQ1QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXNCLGdCQUFtQztRQUN2QyxJQUFJO1lBQ0YsTUFBTXpCLFdBQVcsTUFBTSxJQUFJLENBQUNsQixXQUFXO1lBQ3ZDLE1BQU00QyxhQUFhO21CQUFJLElBQUlDLElBQUkzQixTQUFTRSxJQUFJLENBQUMwQixRQUFRLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsVUFBV0EsUUFBUTFDLFFBQVE7YUFBRztZQUN4RixPQUFPc0MsV0FBV0ssTUFBTSxDQUFDQztRQUMzQixFQUFFLE9BQU83QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7QUFDRjtBQUVBLE1BQU04QixpQkFBaUIsSUFBSXBEO0FBQzNCLGlFQUFlb0QsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGxpYlxcYXBpXFxwcm9kdWN0U2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXBpQ2xpZW50IGZyb20gJy4uLy4uLy4uL2xpYi9hcGkvYXBpQ2xpZW50JztcbmltcG9ydCB7IFByb2R1Y3QsIFByb2R1Y3RzUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzL3VzZXInO1xuXG5leHBvcnQgaW50ZXJmYWNlIFByb2R1Y3RGaWx0ZXJzIHtcbiAgc2VhcmNoPzogc3RyaW5nO1xuICBjYXRlZ29yeT86IHN0cmluZztcbiAgdHlwZT86IHN0cmluZztcbiAgbWluUHJpY2U/OiBudW1iZXI7XG4gIG1heFByaWNlPzogbnVtYmVyO1xuICBpblN0b2NrPzogYm9vbGVhbjtcbiAgc29ydD86IHN0cmluZztcbiAgcGFnZT86IG51bWJlcjtcbiAgbGltaXQ/OiBudW1iZXI7XG59XG5cbmNsYXNzIFByb2R1Y3RTZXJ2aWNlIHtcbiAgLyoqXG4gICAqIEdldCBhbGwgcHJvZHVjdHMgd2l0aCBvcHRpb25hbCBmaWx0ZXJzXG4gICAqL1xuICBhc3luYyBnZXRQcm9kdWN0cyhmaWx0ZXJzOiBQcm9kdWN0RmlsdGVycyA9IHt9KTogUHJvbWlzZTxQcm9kdWN0c1Jlc3BvbnNlPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICAgIFxuICAgICAgLy8gQWRkIGZpbHRlcnMgdG8gcXVlcnkgcGFyYW1zXG4gICAgICBpZiAoZmlsdGVycy5zZWFyY2gpIHtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgnc2VhcmNoJywgZmlsdGVycy5zZWFyY2gpO1xuICAgICAgfVxuICAgICAgaWYgKGZpbHRlcnMuY2F0ZWdvcnkpIHtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgnY2F0ZWdvcnknLCBmaWx0ZXJzLmNhdGVnb3J5KTtcbiAgICAgIH1cbiAgICAgIGlmIChmaWx0ZXJzLnR5cGUpIHtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgndHlwZScsIGZpbHRlcnMudHlwZSk7XG4gICAgICB9XG4gICAgICBpZiAoZmlsdGVycy5taW5QcmljZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ3ByaWNlW2d0ZV0nLCBmaWx0ZXJzLm1pblByaWNlLnRvU3RyaW5nKCkpO1xuICAgICAgfVxuICAgICAgaWYgKGZpbHRlcnMubWF4UHJpY2UgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBwYXJhbXMuYXBwZW5kKCdwcmljZVtsdGVdJywgZmlsdGVycy5tYXhQcmljZS50b1N0cmluZygpKTtcbiAgICAgIH1cbiAgICAgIGlmIChmaWx0ZXJzLmluU3RvY2sgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBwYXJhbXMuYXBwZW5kKCdzdG9jaycsIGZpbHRlcnMuaW5TdG9jay50b1N0cmluZygpKTtcbiAgICAgIH1cbiAgICAgIGlmIChmaWx0ZXJzLnNvcnQpIHtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgnc29ydCcsIGZpbHRlcnMuc29ydCk7XG4gICAgICB9XG4gICAgICBpZiAoZmlsdGVycy5wYWdlKSB7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ3BhZ2UnLCBmaWx0ZXJzLnBhZ2UudG9TdHJpbmcoKSk7XG4gICAgICB9XG4gICAgICBpZiAoZmlsdGVycy5saW1pdCkge1xuICAgICAgICBwYXJhbXMuYXBwZW5kKCdsaW1pdCcsIGZpbHRlcnMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zLnRvU3RyaW5nKCk7XG4gICAgICBjb25zdCB1cmwgPSBgL3Byb2R1Y3RzJHtxdWVyeVN0cmluZyA/IGA/JHtxdWVyeVN0cmluZ31gIDogJyd9YDtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0PFByb2R1Y3RzUmVzcG9uc2U+KHVybCk7XG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcHJvZHVjdHM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBhIHNpbmdsZSBwcm9kdWN0IGJ5IElEXG4gICAqL1xuICBhc3luYyBnZXRQcm9kdWN0KGlkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3RhdHVzOiBzdHJpbmc7IGRhdGE6IFByb2R1Y3QgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQ8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogUHJvZHVjdCB9PihgL3Byb2R1Y3RzLyR7aWR9YCk7XG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcHJvZHVjdDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IHByb2R1Y3Qgd2l0aCBGb3JtRGF0YSAoZm9yIGZpbGUgdXBsb2FkcylcbiAgICovXG4gIGFzeW5jIGNyZWF0ZVByb2R1Y3QoZm9ybURhdGE6IEZvcm1EYXRhKTogUHJvbWlzZTx7IHN0YXR1czogc3RyaW5nOyBkYXRhOiB7IHByb2R1Y3Q6IFByb2R1Y3QgfSB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSBmb3IgYXV0aGVudGljYXRpb25cbiAgICAgIGNvbnN0IHRva2VuID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSA6IG51bGw7XG5cbiAgICAgIGNvbnN0IGhlYWRlcnM6IEhlYWRlcnNJbml0ID0ge307XG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgaGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Rva2VufWA7XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZSBmZXRjaCBkaXJlY3RseSBmb3IgRm9ybURhdGEgdG8gYXZvaWQgYXhpb3MgY29udGVudC10eXBlIGlzc3Vlc1xuICAgICAgLy8gVXNlIE5leHQuanMgQVBJIHByb3h5IHRvIGF2b2lkIENPUlMgaXNzdWVzXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Byb2R1Y3RzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgICAgYm9keTogZm9ybURhdGEsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHsgbWVzc2FnZTogJ0ZhaWxlZCB0byBjcmVhdGUgcHJvZHVjdCcgfSkpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHByb2R1Y3Q6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBhIHByb2R1Y3Qgd2l0aCBGb3JtRGF0YSAoZm9yIGZpbGUgdXBsb2FkcylcbiAgICovXG4gIGFzeW5jIHVwZGF0ZVByb2R1Y3QoaWQ6IHN0cmluZywgZm9ybURhdGE6IEZvcm1EYXRhKTogUHJvbWlzZTx7IHN0YXR1czogc3RyaW5nOyBkYXRhOiB7IHByb2R1Y3Q6IFByb2R1Y3QgfSB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSBmb3IgYXV0aGVudGljYXRpb25cbiAgICAgIGNvbnN0IHRva2VuID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSA6IG51bGw7XG5cbiAgICAgIGNvbnN0IGhlYWRlcnM6IEhlYWRlcnNJbml0ID0ge307XG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgaGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Rva2VufWA7XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZSBmZXRjaCBkaXJlY3RseSBmb3IgRm9ybURhdGEgdG8gYXZvaWQgYXhpb3MgY29udGVudC10eXBlIGlzc3Vlc1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9wcm9kdWN0cy8ke2lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxuICAgICAgICBoZWFkZXJzLFxuICAgICAgICBib2R5OiBmb3JtRGF0YSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoeyBtZXNzYWdlOiAnRmFpbGVkIHRvIHVwZGF0ZSBwcm9kdWN0JyB9KSk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgcHJvZHVjdDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRGVsZXRlIGEgcHJvZHVjdFxuICAgKi9cbiAgYXN5bmMgZGVsZXRlUHJvZHVjdChpZDogc3RyaW5nKTogUHJvbWlzZTx7IHN0YXR1czogc3RyaW5nOyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdG9rZW4gZnJvbSBsb2NhbFN0b3JhZ2UgZm9yIGF1dGhlbnRpY2F0aW9uXG4gICAgICBjb25zdCB0b2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykgOiBudWxsO1xuXG4gICAgICBjb25zdCBoZWFkZXJzOiBIZWFkZXJzSW5pdCA9IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH07XG4gICAgICBcbiAgICAgIGlmICh0b2tlbikge1xuICAgICAgICBoZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7dG9rZW59YDtcbiAgICAgIH1cblxuICAgICAgLy8gVXNlIGZldGNoIGRpcmVjdGx5IGZvciBjb25zaXN0ZW50IGF1dGhvcml6YXRpb24gaGFuZGxpbmdcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcHJvZHVjdHMvJHtpZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICAgIGhlYWRlcnMsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHsgbWVzc2FnZTogJ0ZhaWxlZCB0byBkZWxldGUgcHJvZHVjdCcgfSkpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIEhhbmRsZSAyMDQgTm8gQ29udGVudCByZXNwb25zZVxuICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gMjA0KSB7XG4gICAgICAgIHJldHVybiB7IHN0YXR1czogJ3N1Y2Nlc3MnLCBtZXNzYWdlOiAnUHJvZHVjdCBkZWxldGVkIHN1Y2Nlc3NmdWxseScgfTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiBkYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBwcm9kdWN0OicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgcHJvZHVjdCBjYXRlZ29yaWVzXG4gICAqL1xuICBhc3luYyBnZXRDYXRlZ29yaWVzKCk6IFByb21pc2U8c3RyaW5nW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFByb2R1Y3RzKCk7XG4gICAgICBjb25zdCBjYXRlZ29yaWVzID0gWy4uLm5ldyBTZXQocmVzcG9uc2UuZGF0YS5wcm9kdWN0cy5tYXAocHJvZHVjdCA9PiBwcm9kdWN0LmNhdGVnb3J5KSldO1xuICAgICAgcmV0dXJuIGNhdGVnb3JpZXMuZmlsdGVyKEJvb2xlYW4pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjYXRlZ29yaWVzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cbn1cblxuY29uc3QgcHJvZHVjdFNlcnZpY2UgPSBuZXcgUHJvZHVjdFNlcnZpY2UoKTtcbmV4cG9ydCBkZWZhdWx0IHByb2R1Y3RTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbImFwaUNsaWVudCIsIlByb2R1Y3RTZXJ2aWNlIiwiZ2V0UHJvZHVjdHMiLCJmaWx0ZXJzIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwic2VhcmNoIiwiYXBwZW5kIiwiY2F0ZWdvcnkiLCJ0eXBlIiwibWluUHJpY2UiLCJ1bmRlZmluZWQiLCJ0b1N0cmluZyIsIm1heFByaWNlIiwiaW5TdG9jayIsInNvcnQiLCJwYWdlIiwibGltaXQiLCJxdWVyeVN0cmluZyIsInVybCIsInJlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImVycm9yIiwiY29uc29sZSIsImdldFByb2R1Y3QiLCJpZCIsImNyZWF0ZVByb2R1Y3QiLCJmb3JtRGF0YSIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImhlYWRlcnMiLCJmZXRjaCIsIm1ldGhvZCIsImJvZHkiLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsIm1lc3NhZ2UiLCJFcnJvciIsInN0YXR1cyIsInVwZGF0ZVByb2R1Y3QiLCJkZWxldGVQcm9kdWN0IiwiZ2V0Q2F0ZWdvcmllcyIsImNhdGVnb3JpZXMiLCJTZXQiLCJwcm9kdWN0cyIsIm1hcCIsInByb2R1Y3QiLCJmaWx0ZXIiLCJCb29sZWFuIiwicHJvZHVjdFNlcnZpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/productService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/**\r\n * Format a date to a readable string\r\n */ function formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\n/**\r\n * Format a number as currency\r\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(amount);\n}\n/**\r\n * Truncate a string to a specified length\r\n */ function truncateString(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + '...';\n}\n/**\r\n * Generate a random ID\r\n */ function generateId() {\n    return Math.random().toString(36).substring(2, 9);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7Q0FFQyxHQUNNLFNBQVNBLFdBQVdDLElBQVU7SUFDbkMsT0FBTyxJQUFJQyxLQUFLQyxjQUFjLENBQUMsU0FBUztRQUN0Q0MsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUCxHQUFHQyxNQUFNLENBQUNOO0FBQ1o7QUFFQTs7Q0FFQyxHQUNNLFNBQVNPLGVBQWVDLE1BQWM7SUFDM0MsT0FBTyxJQUFJUCxLQUFLUSxZQUFZLENBQUMsU0FBUztRQUNwQ0MsT0FBTztRQUNQQyxVQUFVO0lBQ1osR0FBR0wsTUFBTSxDQUFDRTtBQUNaO0FBRUE7O0NBRUMsR0FDTSxTQUFTSSxlQUFlQyxHQUFXLEVBQUVDLE1BQWM7SUFDeEQsSUFBSUQsSUFBSUMsTUFBTSxJQUFJQSxRQUFRLE9BQU9EO0lBQ2pDLE9BQU9BLElBQUlFLEtBQUssQ0FBQyxHQUFHRCxVQUFVO0FBQ2hDO0FBRUE7O0NBRUMsR0FDTSxTQUFTRTtJQUNkLE9BQU9DLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO0FBQ2pEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFMSSBDT01QVVRFUlNcXERlc2t0b3BcXGFkbWluXFxjd2EtYWRtaW5cXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogRm9ybWF0IGEgZGF0ZSB0byBhIHJlYWRhYmxlIHN0cmluZ1xyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogRGF0ZSk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KCdlbi1VUycsIHtcclxuICAgIHllYXI6ICdudW1lcmljJyxcclxuICAgIG1vbnRoOiAnbG9uZycsXHJcbiAgICBkYXk6ICdudW1lcmljJyxcclxuICB9KS5mb3JtYXQoZGF0ZSk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBGb3JtYXQgYSBudW1iZXIgYXMgY3VycmVuY3lcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDdXJyZW5jeShhbW91bnQ6IG51bWJlcik6IHN0cmluZyB7XHJcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tVVMnLCB7XHJcbiAgICBzdHlsZTogJ2N1cnJlbmN5JyxcclxuICAgIGN1cnJlbmN5OiAnVVNEJyxcclxuICB9KS5mb3JtYXQoYW1vdW50KTtcclxufVxyXG5cclxuLyoqXHJcbiAqIFRydW5jYXRlIGEgc3RyaW5nIHRvIGEgc3BlY2lmaWVkIGxlbmd0aFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIHRydW5jYXRlU3RyaW5nKHN0cjogc3RyaW5nLCBsZW5ndGg6IG51bWJlcik6IHN0cmluZyB7XHJcbiAgaWYgKHN0ci5sZW5ndGggPD0gbGVuZ3RoKSByZXR1cm4gc3RyO1xyXG4gIHJldHVybiBzdHIuc2xpY2UoMCwgbGVuZ3RoKSArICcuLi4nO1xyXG59XHJcblxyXG4vKipcclxuICogR2VuZXJhdGUgYSByYW5kb20gSURcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA5KTtcclxufVxyXG4iXSwibmFtZXMiOlsiZm9ybWF0RGF0ZSIsImRhdGUiLCJJbnRsIiwiRGF0ZVRpbWVGb3JtYXQiLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJmb3JtYXQiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJ0cnVuY2F0ZVN0cmluZyIsInN0ciIsImxlbmd0aCIsInNsaWNlIiwiZ2VuZXJhdGVJZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/imageUtils.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/imageUtils.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getImageUrls: () => (/* binding */ getImageUrls),\n/* harmony export */   handleImageError: () => (/* binding */ handleImageError)\n/* harmony export */ });\n/**\n * Utility functions for handling image URLs\n */ /**\n * Get the proper image URL for display\n * @param imageFilename - The filename stored in the database\n * @returns The proper URL to access the image\n */ function getImageUrl(imageFilename) {\n    // Return placeholder if no filename\n    if (!imageFilename) {\n        return '/placeholder-product.svg';\n    }\n    // If it's already a full URL, return as is\n    if (imageFilename.startsWith('http://') || imageFilename.startsWith('https://')) {\n        return imageFilename;\n    }\n    // If it's a relative path starting with /, return as is\n    if (imageFilename.startsWith('/')) {\n        return imageFilename;\n    }\n    // For GridFS filenames, use the image proxy\n    return `/api/images/${imageFilename}`;\n}\n/**\n * Get multiple image URLs\n * @param imageFilenames - Array of filenames\n * @returns Array of proper URLs\n */ function getImageUrls(imageFilenames) {\n    return imageFilenames.filter(Boolean).map((filename)=>getImageUrl(filename));\n}\n/**\n * Handle image error by setting a placeholder\n * @param event - The error event from img element\n */ function handleImageError(event) {\n    const target = event.target;\n    if (target.src !== '/placeholder-product.svg') {\n        target.src = '/placeholder-product.svg';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/imageUtils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/sonner","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&page=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&appPaths=%2Fproducts%2Fview%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fview%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();