'use client';

import { useState, useRef } from 'react';
import { VideoBlog } from '@/types/user';
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PlayIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { truncateToWords, truncateText } from '@/utils/textTruncation';

// Responsive Tooltip component for showing full description
interface TooltipProps {
  content: string;
  children: React.ReactNode;
  maxWidth?: string;
}

function Tooltip({ content, children, maxWidth = 'max-w-xs sm:max-w-sm' }: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState<'top' | 'bottom'>('top');
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  if (!content || content.trim() === '') {
    return <>{children}</>;
  }

  const showTooltip = () => {
    setIsVisible(true);
    // Check if tooltip would overflow and adjust position
    setTimeout(() => {
      if (tooltipRef.current) {
        const rect = tooltipRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        if (rect.top < 10) {
          setPosition('bottom');
        } else if (rect.bottom > viewportHeight - 10) {
          setPosition('top');
        } else {
          setPosition('top');
        }
      }
    }, 0);
  };

  const hideTooltip = () => {
    setIsVisible(false);
  };

  return (
    <div
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onTouchStart={showTooltip}
      onTouchEnd={hideTooltip}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={`absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg ${maxWidth} ${
            position === 'top'
              ? 'bottom-full mb-2'
              : 'top-full mt-2'
          } left-1/2 transform -translate-x-1/2`}
          style={{
            // Ensure tooltip doesn't overflow horizontally
            left: 'clamp(0px, 50%, calc(100vw - 100% - 16px))'
          }}
        >
          <div className="break-words text-center">{content}</div>
          {/* Arrow */}
          <div className={`absolute left-1/2 transform -translate-x-1/2 ${
            position === 'top'
              ? 'top-full'
              : 'bottom-full'
          }`}>
            <div className={`border-4 border-transparent ${
              position === 'top'
                ? 'border-t-gray-900'
                : 'border-b-gray-900'
            }`}></div>
          </div>
        </div>
      )}
    </div>
  );
}

interface VideoBlogTableProps {
  videoBlogs: VideoBlog[];
  loading: boolean;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  onPageChange: (page: number) => void;
  onDelete: (videoBlog: VideoBlog) => void;
  onEdit: (id: string) => void;
  onView?: (id: string) => void;
  isDeleting?: boolean;
}

export default function VideoBlogTable({
  videoBlogs,
  loading,
  pagination,
  onPageChange,
  onDelete,
  onEdit,
  onView,
  isDeleting = false,
}: VideoBlogTableProps) {
  const [selectedVideoBlog, setSelectedVideoBlog] = useState<VideoBlog | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };



  const getVideoType = (videoBlog: VideoBlog) => {
    if (videoBlog.youtubeVideoId || videoBlog.youtubeUrl) {
      return 'YouTube';
    }
    return 'Direct URL';
  };

  const handlePreview = (videoBlog: VideoBlog) => {
    setSelectedVideoBlog(videoBlog);
  };

  const closePreview = () => {
    setSelectedVideoBlog(null);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (videoBlogs.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6 text-center">
          <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No video blogs</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new video blog.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {/* Responsive Table Layout - Show on all screen sizes */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 sm:px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 sm:w-20 lg:w-32">
                  Video
                </th>
                <th className="px-2 sm:px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-0 w-1/4 sm:w-1/3">
                  Details
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 sm:w-20 lg:w-24">
                  Category
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12 sm:w-16 lg:w-20">
                  Type
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12 sm:w-16 lg:w-20">
                  Views
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12 sm:w-16 lg:w-20">
                  Status
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 sm:w-20 lg:w-24">
                  Created
                </th>
                <th className="px-1 sm:px-2 lg:px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-12 sm:w-16 lg:w-20">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {videoBlogs.map((videoBlog) => (
                <tr key={videoBlog.id} className="hover:bg-gray-50">
                  <td className="px-2 sm:px-4 lg:px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-12 sm:h-10 sm:w-14 lg:h-12 lg:w-16">
                        <img
                          className="h-8 w-12 sm:h-10 sm:w-14 lg:h-12 lg:w-16 rounded object-cover cursor-pointer hover:opacity-80"
                          src={videoBlog.thumbnailUrl}
                          alt={videoBlog.title}
                          onClick={() => handlePreview(videoBlog)}
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-4">
                    <div className="min-w-0 max-w-20 sm:max-w-32 lg:max-w-48">
                      <div className="text-xs sm:text-sm font-medium text-gray-900 truncate">
                        {truncateText(videoBlog.title, 20)}
                      </div>
                      <div className="text-xs text-gray-500 mt-1 truncate">
                        <Tooltip content={videoBlog.description} maxWidth="max-w-xs">
                          <span className="cursor-help">
                            {truncateToWords(videoBlog.description, 1)}
                          </span>
                        </Tooltip>
                      </div>
                      {videoBlog.duration && (
                        <div className="text-xs text-gray-400 mt-1">
                          {formatDuration(videoBlog.duration)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-1 sm:px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 truncate max-w-16 sm:max-w-20">
                      {videoBlog.category}
                    </span>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap text-xs text-gray-900">
                    <span className="truncate">{getVideoType(videoBlog)}</span>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap text-xs text-gray-900">
                    <span className="truncate ml-4">{videoBlog.views.toLocaleString()}</span>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-1 sm:px-2 py-0.5 rounded-full text-xs font-medium truncate max-w-12 sm:max-w-16 ${
                        videoBlog.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {videoBlog.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap text-xs text-gray-500">
                    <span className="truncate">{formatDate(videoBlog.createdAt)}</span>
                  </td>
                  <td className="px-1 sm:px-2 lg:px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-0.5">
                      {onView && (
                        <button
                          onClick={() => {
                            const id = videoBlog.id || videoBlog._id;
                            console.log('Video blog ID:', id, 'Full object:', videoBlog);
                            if (id) {
                              onView(id);
                            } else {
                              console.error('No valid ID found for video blog:', videoBlog);
                            }
                          }}
                          className="text-green-600 hover:text-green-900 p-0.5 sm:p-1 rounded-md hover:bg-green-50"
                          title="View Details"
                        >
                          <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => {
                          const id = videoBlog.id || videoBlog._id;
                          if (id) onEdit(id);
                        }}
                        className="text-indigo-600 hover:text-indigo-900 p-0.5 sm:p-1 rounded-md hover:bg-indigo-50"
                        title="Edit"
                      >
                        <PencilIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                      <button
                        onClick={() => onDelete(videoBlog)}
                        disabled={isDeleting}
                        className={`p-0.5 sm:p-1 rounded-md transition-colors ${
                          isDeleting
                            ? 'text-red-400 cursor-not-allowed'
                            : 'text-red-600 hover:text-red-900 hover:bg-red-50'
                        }`}
                        title="Delete"
                      >
                        <TrashIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
                  </span>{' '}
                  of <span className="font-medium">{pagination.totalItems}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => onPageChange(pagination.currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNumber = Math.max(1, pagination.currentPage - 2) + i;
                    if (pageNumber > pagination.totalPages) return null;
                    
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => onPageChange(pageNumber)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pageNumber === pagination.currentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => onPageChange(pagination.currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Preview Modal */}
      {selectedVideoBlog && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={closePreview}></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      {selectedVideoBlog.title}
                    </h3>
                    
                    <div className="aspect-w-16 aspect-h-9 mb-4">
                      {selectedVideoBlog.youtubeVideoId ? (
                        <iframe
                          src={`https://www.youtube.com/embed/${selectedVideoBlog.youtubeVideoId}`}
                          title={selectedVideoBlog.title}
                          className="w-full h-64 sm:h-96"
                          allowFullScreen
                        />
                      ) : (
                        <video
                          src={selectedVideoBlog.videoUrl}
                          poster={selectedVideoBlog.thumbnailUrl}
                          controls
                          className="w-full h-64 sm:h-96 object-cover"
                        />
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-4">
                      {selectedVideoBlog.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {selectedVideoBlog.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closePreview}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
