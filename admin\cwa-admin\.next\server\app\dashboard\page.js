/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(rsc)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQUxJIENPTVBVVEVSU1xcRGVza3RvcFxcYWRtaW5cXGN3YS1hZG1pblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a96de9275224\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFMSSBDT01QVVRFUlNcXERlc2t0b3BcXGFkbWluXFxjd2EtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE5NmRlOTI3NTIyNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/Providers */ \"(rsc)/./src/components/providers/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"CWA Admin Dashboard\",\n    description: \"Admin dashboard for CWA application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDa0M7QUFZbEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLHVFQUFTQTswQkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCBQcm92aWRlcnMgZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNXQSBBZG1pbiBEYXNoYm9hcmRcIixcbiAgZGVzY3JpcHRpb246IFwiQWRtaW4gZGFzaGJvYXJkIGZvciBDV0EgYXBwbGljYXRpb25cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\components\\providers\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./lib/api/apiClient.ts":
/*!******************************!*\
  !*** ./lib/api/apiClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(ssr)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClipboardDocumentListIcon,CurrencyDollarIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/userService */ \"(ssr)/./src/services/userService.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_api_productService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/productService */ \"(ssr)/./src/lib/api/productService.ts\");\n/* harmony import */ var _lib_api_orderService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/orderService */ \"(ssr)/./src/lib/api/orderService.ts\");\n/* harmony import */ var _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/videoBlogService */ \"(ssr)/./src/lib/api/videoBlogService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [totalProducts, setTotalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [totalOrders, setTotalOrders] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [totalRevenue, setTotalRevenue] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [totalVideoBlogs, setTotalVideoBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [recentActivities, setRecentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"Dashboard.useEffect.fetchDashboardData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Fetch all data in parallel\n                        const [usersResponse, productsResponse, orderStats, videoBlogsResponse] = await Promise.all([\n                            _services_userService__WEBPACK_IMPORTED_MODULE_3__.userService.getUsers().catch({\n                                \"Dashboard.useEffect.fetchDashboardData\": (err)=>{\n                                    console.error('Error fetching users:', err);\n                                    return [];\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"]),\n                            _lib_api_productService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getProducts().catch({\n                                \"Dashboard.useEffect.fetchDashboardData\": (err)=>{\n                                    console.error('Error fetching products:', err);\n                                    return {\n                                        status: 'error',\n                                        results: 0,\n                                        data: {\n                                            products: []\n                                        }\n                                    };\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"]),\n                            _lib_api_orderService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getOrderStats().catch({\n                                \"Dashboard.useEffect.fetchDashboardData\": (err)=>{\n                                    console.error('Error fetching order stats:', err);\n                                    return {\n                                        totalOrders: 0,\n                                        totalRevenue: 0\n                                    };\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"]),\n                            _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getVideoBlogs({\n                                limit: 1000\n                            }).catch({\n                                \"Dashboard.useEffect.fetchDashboardData\": (err)=>{\n                                    console.error('Error fetching video blogs:', err);\n                                    return {\n                                        data: {\n                                            videoBlogs: []\n                                        }\n                                    };\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"])\n                        ]);\n                        // Update state with fetched data\n                        setTotalUsers(Array.isArray(usersResponse) ? usersResponse.length : 0);\n                        setTotalProducts(productsResponse.data?.products?.length || 0);\n                        setTotalOrders(orderStats.totalOrders || 0);\n                        setTotalRevenue(orderStats.totalRevenue || 0);\n                        setTotalVideoBlogs(videoBlogsResponse.data?.videoBlogs?.length || 0);\n                        // Create recent activities from available data\n                        const activities = [];\n                        // Add recent users (last 5)\n                        if (Array.isArray(usersResponse) && usersResponse.length > 0) {\n                            const typedUsers = usersResponse;\n                            const recentUsers = typedUsers.sort({\n                                \"Dashboard.useEffect.fetchDashboardData.recentUsers\": (a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n                            }[\"Dashboard.useEffect.fetchDashboardData.recentUsers\"]).slice(0, 3);\n                            recentUsers.forEach({\n                                \"Dashboard.useEffect.fetchDashboardData\": (user)=>{\n                                    activities.push({\n                                        id: `user-${user.id}`,\n                                        user: {\n                                            name: user.name || 'Unknown User',\n                                            initials: (user.name || 'U').split(' ').map({\n                                                \"Dashboard.useEffect.fetchDashboardData\": (n)=>n[0]\n                                            }[\"Dashboard.useEffect.fetchDashboardData\"]).join('').toUpperCase().slice(0, 2)\n                                        },\n                                        action: 'Registered account',\n                                        time: new Date(user.createdAt),\n                                        status: 'success',\n                                        type: 'user'\n                                    });\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"]);\n                        }\n                        // Add recent video blogs\n                        if (videoBlogsResponse.data?.videoBlogs?.length > 0) {\n                            const recentVideos = videoBlogsResponse.data.videoBlogs.sort({\n                                \"Dashboard.useEffect.fetchDashboardData.recentVideos\": (a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n                            }[\"Dashboard.useEffect.fetchDashboardData.recentVideos\"]).slice(0, 2);\n                            recentVideos.forEach({\n                                \"Dashboard.useEffect.fetchDashboardData\": (video)=>{\n                                    activities.push({\n                                        id: `video-${video.id}`,\n                                        user: {\n                                            name: 'Admin',\n                                            initials: 'AD'\n                                        },\n                                        action: `Published video: ${(video.title || 'Untitled Video').substring(0, 30)}...`,\n                                        time: new Date(video.createdAt),\n                                        status: 'success',\n                                        type: 'video'\n                                    });\n                                }\n                            }[\"Dashboard.useEffect.fetchDashboardData\"]);\n                        }\n                        // Sort activities by time and take the most recent 5\n                        const sortedActivities = activities.sort({\n                            \"Dashboard.useEffect.fetchDashboardData.sortedActivities\": (a, b)=>b.time.getTime() - a.time.getTime()\n                        }[\"Dashboard.useEffect.fetchDashboardData.sortedActivities\"]).slice(0, 5);\n                        setRecentActivities(sortedActivities);\n                    } catch (error) {\n                        console.error('Error fetching dashboard data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Dashboard.useEffect.fetchDashboardData\"];\n            fetchDashboardData();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const formatTimeAgo = (date)=>{\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) {\n            return `${diffInSeconds} seconds ago`;\n        } else if (diffInSeconds < 3600) {\n            const minutes = Math.floor(diffInSeconds / 60);\n            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n        } else if (diffInSeconds < 86400) {\n            const hours = Math.floor(diffInSeconds / 3600);\n            return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n        } else {\n            const days = Math.floor(diffInSeconds / 86400);\n            return `${days} day${days > 1 ? 's' : ''} ago`;\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const baseClasses = \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\";\n        switch(status){\n            case 'success':\n                return `${baseClasses} bg-green-100 text-green-800`;\n            case 'warning':\n                return `${baseClasses} bg-yellow-100 text-yellow-800`;\n            case 'error':\n                return `${baseClasses} bg-red-100 text-red-800`;\n            default:\n                return `${baseClasses} bg-gray-100 text-gray-800`;\n        }\n    };\n    const getAvatarColor = (type)=>{\n        switch(type){\n            case 'user':\n                return 'from-blue-500 to-blue-600';\n            case 'video':\n                return 'from-purple-500 to-purple-600';\n            case 'order':\n                return 'from-green-500 to-green-600';\n            default:\n                return 'from-gray-500 to-gray-600';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 sm:mb-6 lg:mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 lg:p-8 text-white shadow-custom-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-1 sm:mb-2\",\n                            children: [\n                                \"Welcome back, \",\n                                user?.name,\n                                \"! \\uD83D\\uDC4B\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 text-sm sm:text-base lg:text-lg\",\n                            children: \"Here's what's happening with your admin dashboard today.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6 lg:mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-blue-500 hover:shadow-md transition-shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse bg-gray-200 h-8 w-16 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this) : totalUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                            children: \"Registered users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 sm:p-3 bg-blue-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-green-500 hover:shadow-md transition-shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse bg-gray-200 h-8 w-16 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this) : totalProducts.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                            children: \"Available products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 sm:p-3 bg-green-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-yellow-500 hover:shadow-md transition-shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider\",\n                                            children: \"Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse bg-gray-200 h-8 w-16 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this) : totalOrders.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                            children: \"Total orders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 sm:p-3 bg-yellow-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-purple-500 hover:shadow-md transition-shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider\",\n                                            children: \"Revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse bg-gray-200 h-8 w-20 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this) : `$${totalRevenue.toLocaleString()}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                            children: \"Total revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 sm:p-3 bg-purple-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6 lg:mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-indigo-500 hover:shadow-md transition-shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider\",\n                                        children: \"Video Blogs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-pulse bg-gray-200 h-8 w-16 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this) : totalVideoBlogs.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                        children: \"Published videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 sm:p-3 bg-indigo-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClipboardDocumentListIcon_CurrencyDollarIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg sm:rounded-xl shadow-custom overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b\",\n                        style: {\n                            borderColor: 'var(--border-color)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base sm:text-lg md:text-xl font-semibold text-gray-900\",\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-gray-600 mt-1\",\n                                children: \"Latest user activities and system events\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y\",\n                            style: {\n                                borderColor: 'var(--border-color)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    style: {\n                                        backgroundColor: 'var(--neutral-50)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\",\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\",\n                                                children: \"Action\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\",\n                                                children: \"Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y\",\n                                    style: {\n                                        borderColor: 'var(--border-color)'\n                                    },\n                                    children: isLoading ? // Loading skeleton\n                                    [\n                                        ...Array(3)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-slate-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 md:w-8 md:h-8 rounded-full bg-gray-200 animate-pulse mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded w-24 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-200 rounded w-32 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-200 rounded w-20 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-200 rounded-full w-16 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)) : recentActivities.length > 0 ? recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-slate-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-7 h-7 md:w-8 md:h-8 rounded-full bg-gradient-to-r ${getAvatarColor(activity.type)} flex items-center justify-center text-white font-semibold text-sm mr-3`,\n                                                                children: activity.user.initials\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900 text-sm md:text-base\",\n                                                                children: activity.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-gray-900 text-sm md:text-base\",\n                                                    children: activity.action\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-gray-500 text-sm\",\n                                                    children: formatTimeAgo(activity.time)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 md:px-6 py-3 md:py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: getStatusBadge(activity.status),\n                                                        children: activity.status.charAt(0).toUpperCase() + activity.status.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 4,\n                                            className: \"px-4 md:px-6 py-8 text-center text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl mb-2\",\n                                                        children: \"\\uD83D\\uDCCA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"No recent activity to display\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden space-y-3 p-3 sm:p-4\",\n                        children: isLoading ? // Loading skeleton for mobile\n                        [\n                            ...Array(3)\n                        ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 sm:p-4 border border-gray-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gray-200 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 rounded w-24 animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-6 bg-gray-200 rounded-full w-16 animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded w-32 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-3 bg-gray-200 rounded w-20 animate-pulse mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 15\n                            }, this)) : recentActivities.length > 0 ? recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r ${getAvatarColor(activity.type)} flex items-center justify-center text-white font-semibold text-sm`,\n                                            children: activity.user.initials\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: activity.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: getStatusBadge(activity.status),\n                                                            children: activity.status.charAt(0).toUpperCase() + activity.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: activity.action\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: formatTimeAgo(activity.time)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, this)\n                            }, activity.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 text-center text-gray-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-2\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"No recent activity to display\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children }) {\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, don't render children (redirect will happen)\n    if (!isAuthenticated) {\n        return null;\n    }\n    // If authenticated, render the protected content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50\",\n            style: {\n                backgroundColor: 'var(--background)'\n            },\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onClose: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col lg:ml-64 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onMenuClick: ()=>setSidebarOpen(true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-3 sm:p-4 md:p-6 overflow-y-auto bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-full xl:max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRGFzaGJvYXJkTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDaUM7QUFDRDtBQUNGO0FBQ3NCO0FBRXJDLFNBQVNJLGdCQUFnQixFQUN0Q0MsUUFBUSxFQUdUO0lBQ0MsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdQLCtDQUFRQSxDQUFDO0lBRS9DLHFCQUNFLDhEQUFDRyw0REFBY0E7a0JBQ2IsNEVBQUNLO1lBQUlDLFdBQVU7WUFBMkJDLE9BQU87Z0JBQUVDLGlCQUFpQjtZQUFvQjs7Z0JBRXJGTCw2QkFDQyw4REFBQ0U7b0JBQ0NDLFdBQVU7b0JBQ1ZHLFNBQVMsSUFBTUwsZUFBZTs7Ozs7OzhCQUlsQyw4REFBQ04sZ0RBQU9BO29CQUFDWSxRQUFRUDtvQkFBYVEsU0FBUyxJQUFNUCxlQUFlOzs7Ozs7OEJBRTVELDhEQUFDQztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNQLCtDQUFNQTs0QkFBQ2EsYUFBYSxJQUFNUixlQUFlOzs7Ozs7c0NBQzFDLDhEQUFDUzs0QkFBS1AsV0FBVTtzQ0FDZCw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQUxJIENPTVBVVEVSU1xcRGVza3RvcFxcYWRtaW5cXGN3YS1hZG1pblxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXERhc2hib2FyZExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgU2lkZWJhciBmcm9tICcuL1NpZGViYXInO1xuaW1wb3J0IEhlYWRlciBmcm9tICcuL0hlYWRlcic7XG5pbXBvcnQgUHJvdGVjdGVkUm91dGUgZnJvbSAnLi4vYXV0aC9Qcm90ZWN0ZWRSb3V0ZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICBjb25zdCBbc2lkZWJhck9wZW4sIHNldFNpZGViYXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICByZXR1cm4gKFxuICAgIDxQcm90ZWN0ZWRSb3V0ZT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1ncmF5LTUwXCIgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tYmFja2dyb3VuZCknIH19PlxuICAgICAgICB7LyogTW9iaWxlIHNpZGViYXIgYmFja2Ryb3AgKi99XG4gICAgICAgIHtzaWRlYmFyT3BlbiAmJiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTQwIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgbGc6aGlkZGVuIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAgPFNpZGViYXIgaXNPcGVuPXtzaWRlYmFyT3Blbn0gb25DbG9zZT17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfSAvPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgbGc6bWwtNjQgbWluLXctMFwiPlxuICAgICAgICAgIDxIZWFkZXIgb25NZW51Q2xpY2s9eygpID0+IHNldFNpZGViYXJPcGVuKHRydWUpfSAvPlxuICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLTMgc206cC00IG1kOnAtNiBvdmVyZmxvdy15LWF1dG8gYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1mdWxsIHhsOm1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbWFpbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiU2lkZWJhciIsIkhlYWRlciIsIlByb3RlY3RlZFJvdXRlIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJvbkNsaWNrIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uTWVudUNsaWNrIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ onMenuClick }) {\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    const getPageTitle = ()=>{\n        switch(pathname){\n            case '/dashboard':\n                return 'Dashboard';\n            case '/users':\n                return 'User Management';\n            case '/products':\n                return 'Product Management';\n            case '/video-blogs':\n                return 'Video Blog Management';\n            case '/video-blogs/add':\n                return 'Add Video Blog';\n            case '/orders':\n                return 'Order Management';\n            case '/settings':\n                return 'Settings';\n            default:\n                if (pathname.startsWith('/video-blogs/') && pathname.includes('/edit')) {\n                    return 'Edit Video Blog';\n                }\n                return 'CWA Admin Dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-14 sm:h-16 flex items-center px-3 sm:px-4 md:px-6 border-b\",\n        style: {\n            backgroundColor: 'var(--header-background)',\n            boxShadow: 'var(--header-shadow)',\n            borderColor: 'var(--border-color)'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMenuClick,\n                className: \"lg:hidden p-1.5 sm:p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-colors mr-2 sm:mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base sm:text-lg md:text-xl font-semibold truncate\",\n                        style: {\n                            color: 'var(--foreground)'\n                        },\n                        children: getPageTitle()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs sm:text-sm text-slate-500 mt-0.5 hidden sm:block\",\n                        children: \"Welcome back to your admin dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-1.5 sm:p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-0.5 sm:top-1 right-0.5 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3 text-slate-700 hover:text-slate-900 focus:outline-none p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm font-medium\",\n                                            children: user?.name || 'Admin User'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 hidden md:block\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm\",\n                                        children: user?.name ? getInitials(user.name) : 'AU'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 hidden sm:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose }) {\n    const { logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: '/users',\n            label: 'Users',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: '/products',\n            label: 'Products',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: '/video-blogs',\n            label: 'Video Blogs',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: '/orders',\n            label: 'Orders',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    const handleNavClick = ()=>{\n        // Close sidebar on mobile when navigation item is clicked\n        if (window.innerWidth < 1024) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `\n        w-64 h-screen fixed left-0 top-0 z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n        lg:translate-x-0 bg-slate-800 shadow-xl\n      `,\n        style: {\n            backgroundColor: 'var(--sidebar-background)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 lg:p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6 lg:mb-8 lg:block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl lg:text-2xl font-bold text-white\",\n                                    children: \"CWA Admin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs lg:text-sm text-slate-300 mt-1\",\n                                    children: \"Management Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"lg:hidden p-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1 lg:space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: handleNavClick,\n                                    className: `flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg transition-all duration-200 ${isActive ? 'bg-blue-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm lg:text-base\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 lg:mt-8 pt-4 lg:pt-6 border-t border-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 w-full text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm lg:text-base\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                richColors: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHcUQ7QUFDcEI7QUFNbEIsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQzVELHFCQUNFLDhEQUFDSCw4REFBWUE7O1lBQ1ZHOzBCQUNELDhEQUFDRiwyQ0FBT0E7Z0JBQUNHLFVBQVM7Z0JBQVlDLFVBQVU7Ozs7Ozs7Ozs7OztBQUc5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdzb25uZXInO1xyXG5cclxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm92aWRlcnNQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDxUb2FzdGVyIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgcmljaENvbG9ycyAvPlxyXG4gICAgPC9BdXRoUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicG9zaXRpb24iLCJyaWNoQ29sb3JzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/apiClient */ \"(ssr)/./src/lib/api/apiClient.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null\n};\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        const userId = localStorage.getItem('userId');\n                        const name = localStorage.getItem('name');\n                        const email = localStorage.getItem('email');\n                        const phone = localStorage.getItem('phone');\n                        const isAdmin = localStorage.getItem('isAdmin');\n                        const createdAt = localStorage.getItem('createdAt');\n                        if (token && userId) {\n                            setAuthState({\n                                user: {\n                                    id: userId,\n                                    name: name || 'User',\n                                    email: email || '',\n                                    phone: phone || '',\n                                    isAdmin: isAdmin === 'true',\n                                    createdAt: createdAt || new Date().toISOString()\n                                },\n                                isAuthenticated: true,\n                                isLoading: false,\n                                error: null\n                            });\n                        } else {\n                            setAuthState({\n                                ...initialState,\n                                isLoading: false\n                            });\n                        }\n                    } catch (error) {\n                        console.log(error);\n                        setAuthState({\n                            ...initialState,\n                            isLoading: false\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post('/auth/login', {\n                email,\n                password\n            });\n            console.log(\"Login response:\", response.data);\n            if (response.data.status === 'success') {\n                const { user, token } = response.data.data;\n                // Store auth data in localStorage\n                localStorage.setItem('token', token);\n                localStorage.setItem('userId', user.id);\n                localStorage.setItem('name', user.name);\n                localStorage.setItem('email', user.email);\n                localStorage.setItem('phone', user.phone || '');\n                localStorage.setItem('isAdmin', user.isAdmin ? 'true' : 'false');\n                localStorage.setItem('createdAt', user.createdAt || new Date().toISOString());\n                // Update auth state\n                setAuthState({\n                    user: {\n                        id: user.id,\n                        name: user.name,\n                        email: user.email,\n                        phone: user.phone || '',\n                        isAdmin: user.isAdmin,\n                        createdAt: user.createdAt || new Date().toISOString()\n                    },\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged in successfully!');\n                // Check if user is admin\n                // if (user.isAdmin) {\n                router.push('/dashboard');\n            // } else {\n            //   throw new Error('Access denied. Admin privileges required.');\n            // }\n            } else {\n                throw new Error('Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Invalid email or password';\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n        }\n    };\n    // Logout function\n    const logout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('name');\n        localStorage.removeItem('email');\n        localStorage.removeItem('phone');\n        localStorage.removeItem('isAdmin');\n        localStorage.removeItem('createdAt');\n        // Reset auth state\n        setAuthState(initialState);\n        // Redirect to login\n        router.push('/auth/login');\n        sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged out successfully!');\n    };\n    // Function to directly set auth state (used by auth-callback)\n    const setAuthStateDirectly = (state)=>{\n        setAuthState(state);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            logout,\n            setAuthState: setAuthStateDirectly\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU9jO0FBQzhCO0FBQ2hCO0FBRWdCO0FBRTVDLE1BQU1RLGVBQXVCO0lBQ3pCQyxNQUFLO0lBQ0xDLGlCQUFnQjtJQUNoQkMsV0FBVTtJQUNWQyxPQUFNO0FBQ1Y7QUFDQSxNQUFNQyw0QkFBWVosb0RBQWFBLENBQTRCYTtBQUNwRCxNQUFNQyxlQUFhLENBQUMsRUFBRUMsUUFBUSxFQUEyQjtJQUM1RCxNQUFNLENBQUNDLFdBQVVDLGFBQWEsR0FBQ2hCLCtDQUFRQSxDQUFZTTtJQUNuRCxNQUFNVyxTQUFPZCwwREFBU0E7SUFFdEJGLGdEQUFTQTtrQ0FBQztZQUNOLE1BQU1pQjtvREFBWTtvQkFDZCxJQUFJO3dCQUNBLE1BQU1DLFFBQU1DLGFBQWFDLE9BQU8sQ0FBQzt3QkFDckMsTUFBTUMsU0FBT0YsYUFBYUMsT0FBTyxDQUFDO3dCQUNsQyxNQUFNRSxPQUFLSCxhQUFhQyxPQUFPLENBQUM7d0JBQ2hDLE1BQU1HLFFBQU1KLGFBQWFDLE9BQU8sQ0FBQzt3QkFDakMsTUFBTUksUUFBTUwsYUFBYUMsT0FBTyxDQUFDO3dCQUNqQyxNQUFNSyxVQUFRTixhQUFhQyxPQUFPLENBQUM7d0JBQ25DLE1BQU1NLFlBQVVQLGFBQWFDLE9BQU8sQ0FBQzt3QkFDckMsSUFBR0YsU0FBU0csUUFBTzs0QkFDZk4sYUFDSTtnQ0FDSVQsTUFBSztvQ0FDRHFCLElBQUdOO29DQUNIQyxNQUFLQSxRQUFPO29DQUNaQyxPQUFNQSxTQUFRO29DQUNkQyxPQUFNQSxTQUFRO29DQUNkQyxTQUFRQSxZQUFZO29DQUNwQkMsV0FBVUEsYUFBYSxJQUFJRSxPQUFPQyxXQUFXO2dDQUNqRDtnQ0FDQXRCLGlCQUFnQjtnQ0FDaEJDLFdBQVU7Z0NBQ1ZDLE9BQU07NEJBRVY7d0JBRVIsT0FDSTs0QkFDQU0sYUFBYTtnQ0FDVCxHQUFHVixZQUFZO2dDQUNmRyxXQUFVOzRCQUNkO3dCQUNKO29CQUNKLEVBQ0gsT0FBT0MsT0FBTzt3QkFDSHFCLFFBQVFDLEdBQUcsQ0FBQ3RCO3dCQUNaTSxhQUFhOzRCQUNULEdBQUdWLFlBQVk7NEJBQ2ZHLFdBQVU7d0JBQ2Q7b0JBQ0o7Z0JBQ0o7O1lBQ0FTO1FBQ0E7aUNBQUUsRUFBRTtJQUNKLE1BQU1lLFFBQU0sT0FBUVQsT0FBYVU7UUFDakNsQixhQUFhLENBQUNtQixPQUFRO2dCQUNsQixHQUFHQSxJQUFJO2dCQUFDMUIsV0FBVTtnQkFBS0MsT0FBTTtZQUNqQztRQUNBLElBQUk7WUFDQSxNQUFNMEIsV0FBUyxNQUFNL0IsMERBQVNBLENBQUNnQyxJQUFJLENBQUMsZUFBYztnQkFDOUNiO2dCQUFNVTtZQUNWO1lBQ0FILFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJJLFNBQVNFLElBQUk7WUFDaEQsSUFBSUYsU0FBU0UsSUFBSSxDQUFDQyxNQUFNLEtBQUssV0FBVztnQkFDeEMsTUFBTSxFQUFFaEMsSUFBSSxFQUFFWSxLQUFLLEVBQUUsR0FBR2lCLFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFFMUMsa0NBQWtDO2dCQUNsQ2xCLGFBQWFvQixPQUFPLENBQUMsU0FBU3JCO2dCQUM5QkMsYUFBYW9CLE9BQU8sQ0FBQyxVQUFVakMsS0FBS3FCLEVBQUU7Z0JBQ3RDUixhQUFhb0IsT0FBTyxDQUFDLFFBQVFqQyxLQUFLZ0IsSUFBSTtnQkFDdENILGFBQWFvQixPQUFPLENBQUMsU0FBU2pDLEtBQUtpQixLQUFLO2dCQUN4Q0osYUFBYW9CLE9BQU8sQ0FBQyxTQUFTakMsS0FBS2tCLEtBQUssSUFBSTtnQkFDNUNMLGFBQWFvQixPQUFPLENBQUMsV0FBV2pDLEtBQUttQixPQUFPLEdBQUcsU0FBUztnQkFDeEROLGFBQWFvQixPQUFPLENBQUMsYUFBYWpDLEtBQUtvQixTQUFTLElBQUksSUFBSUUsT0FBT0MsV0FBVztnQkFFMUUsb0JBQW9CO2dCQUNwQmQsYUFBYTtvQkFDWFQsTUFBTTt3QkFDSnFCLElBQUlyQixLQUFLcUIsRUFBRTt3QkFDWEwsTUFBTWhCLEtBQUtnQixJQUFJO3dCQUNmQyxPQUFPakIsS0FBS2lCLEtBQUs7d0JBQ2pCQyxPQUFPbEIsS0FBS2tCLEtBQUssSUFBSTt3QkFDckJDLFNBQVNuQixLQUFLbUIsT0FBTzt3QkFDckJDLFdBQVdwQixLQUFLb0IsU0FBUyxJQUFJLElBQUlFLE9BQU9DLFdBQVc7b0JBQ3JEO29CQUNBdEIsaUJBQWlCO29CQUNqQkMsV0FBVztvQkFDWEMsT0FBTztnQkFDVDtnQkFFQU4seUNBQUtBLENBQUNxQyxPQUFPLENBQUM7Z0JBRWQseUJBQXlCO2dCQUN6QixzQkFBc0I7Z0JBQ3BCeEIsT0FBT3lCLElBQUksQ0FBQztZQUNkLFdBQVc7WUFDWCxrRUFBa0U7WUFDbEUsSUFBSTtZQUNOLE9BQU87Z0JBQ0wsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPakMsT0FBZ0I7WUFDdkJxQixRQUFRckIsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsTUFBTWtDLGVBQWVsQyxpQkFBaUJpQyxRQUFRakMsTUFBTW1DLE9BQU8sR0FBRztZQUM5RDdCLGFBQWEsQ0FBQ21CLE9BQVU7b0JBQ3RCLEdBQUdBLElBQUk7b0JBQ1AxQixXQUFXO29CQUNYQyxPQUFPa0M7Z0JBQ1Q7WUFDQXhDLHlDQUFLQSxDQUFDTSxLQUFLLENBQUNrQztRQUNkO0lBQ0Y7SUFDQSxrQkFBa0I7SUFDbEIsTUFBTUUsU0FBUztRQUNiLHFCQUFxQjtRQUNyQjFCLGFBQWEyQixVQUFVLENBQUM7UUFDeEIzQixhQUFhMkIsVUFBVSxDQUFDO1FBQ3hCM0IsYUFBYTJCLFVBQVUsQ0FBQztRQUN4QjNCLGFBQWEyQixVQUFVLENBQUM7UUFDeEIzQixhQUFhMkIsVUFBVSxDQUFDO1FBQ3hCM0IsYUFBYTJCLFVBQVUsQ0FBQztRQUN4QjNCLGFBQWEyQixVQUFVLENBQUM7UUFFeEIsbUJBQW1CO1FBQ25CL0IsYUFBYVY7UUFFYixvQkFBb0I7UUFDcEJXLE9BQU95QixJQUFJLENBQUM7UUFFWnRDLHlDQUFLQSxDQUFDcUMsT0FBTyxDQUFDO0lBQ2hCO0lBRUEsOERBQThEO0lBQzlELE1BQU1PLHVCQUF1QixDQUFDQztRQUM1QmpDLGFBQWFpQztJQUNmO0lBRUEscUJBQ0UsOERBQUN0QyxZQUFZdUMsUUFBUTtRQUNuQkMsT0FBTztZQUNMLEdBQUdwQyxTQUFTO1lBQ1prQjtZQUNBYTtZQUNBOUIsY0FBY2dDO1FBQ2hCO2tCQUVDbEM7Ozs7OztBQUdQLEVBQUU7QUFDRixrQ0FBa0M7QUFDM0IsTUFBTXNDLFVBQVU7SUFDckIsTUFBTUMsVUFBVW5ELGlEQUFVQSxDQUFDUztJQUMzQixJQUFJMEMsWUFBWXpDLFdBQVc7UUFDekIsTUFBTSxJQUFJK0IsTUFBTTtJQUNsQjtJQUNBLE9BQU9VO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbnRleHRcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcbmltcG9ydCBSZWFjdCAse1xyXG4gICAgY3JlYXRlQ29udGV4dCxcclxuICAgIHVzZVN0YXRlLFxyXG4gICAgdXNlRWZmZWN0LFxyXG4gICAgUmVhY3ROb2RlLFxyXG4gICAgdXNlQ29udGV4dCxcclxufSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHt0b2FzdH0gZnJvbSAnc29ubmVyJ1xyXG5pbXBvcnQge0F1dGhTdGF0ZSwgQXV0aENvbnRleHRUeXBlfSBmcm9tICcuLi90eXBlcy91c2VyJztcclxuaW1wb3J0IGFwaUNsaWVudCBmcm9tICdAL2xpYi9hcGkvYXBpQ2xpZW50JztcclxuXHJcbmNvbnN0IGluaXRpYWxTdGF0ZTpBdXRoU3RhdGU9e1xyXG4gICAgdXNlcjpudWxsLFxyXG4gICAgaXNBdXRoZW50aWNhdGVkOmZhbHNlLFxyXG4gICAgaXNMb2FkaW5nOnRydWUsXHJcbiAgICBlcnJvcjpudWxsXHJcbn1cclxuY29uc3QgQXV0aENvbnRleHQ9Y3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGV8dW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyPSh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSA9PntcclxuICAgIGNvbnN0IFthdXRoU3RhdGUsc2V0QXV0aFN0YXRlXT11c2VTdGF0ZTxBdXRoU3RhdGU+KGluaXRpYWxTdGF0ZSlcclxuICAgIGNvbnN0IHJvdXRlcj11c2VSb3V0ZXIoKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKT0+e1xyXG4gICAgICAgIGNvbnN0IGNoZWNrQXV0aCA9ICgpPT57XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0b2tlbj1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxyXG4gICAgICAgICAgICBjb25zdCB1c2VySWQ9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpO1xyXG4gICAgICAgICAgICBjb25zdCBuYW1lPWxvY2FsU3RvcmFnZS5nZXRJdGVtKCduYW1lJylcclxuICAgICAgICAgICAgY29uc3QgZW1haWw9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2VtYWlsJylcclxuICAgICAgICAgICAgY29uc3QgcGhvbmU9bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Bob25lJylcclxuICAgICAgICAgICAgY29uc3QgaXNBZG1pbj1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaXNBZG1pbicpXHJcbiAgICAgICAgICAgIGNvbnN0IGNyZWF0ZWRBdD1sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3JlYXRlZEF0JylcclxuICAgICAgICAgICAgaWYodG9rZW4gJiYgdXNlcklkKXtcclxuICAgICAgICAgICAgICAgIHNldEF1dGhTdGF0ZShcclxuICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXI6e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6dXNlcklkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTpuYW1lIHx8J1VzZXInLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6ZW1haWwgfHwnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBob25lOnBob25lIHx8JycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0FkbWluOmlzQWRtaW4gPT09ICd0cnVlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZWRBdDpjcmVhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZDp0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc6ZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yOm51bGxcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGVsc2V7XHJcbiAgICAgICAgICAgICAgICBzZXRBdXRoU3RhdGUoe1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLmluaXRpYWxTdGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc6ZmFsc2VcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnJvcilcclxuICAgICAgICAgICAgICAgIHNldEF1dGhTdGF0ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uaW5pdGlhbFN0YXRlLFxyXG4gICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZzpmYWxzZVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBjaGVja0F1dGgoKVxyXG4gICAgICAgIH0sW10pO1xyXG4gICAgICAgIGNvbnN0IGxvZ2luPWFzeW5jICAoZW1haWw6c3RyaW5nLHBhc3N3b3JkOnN0cmluZyk9PntcclxuICAgICAgICBzZXRBdXRoU3RhdGUoKHByZXYpPT4oe1xyXG4gICAgICAgICAgICAuLi5wcmV2LGlzTG9hZGluZzp0cnVlLGVycm9yOm51bGxcclxuICAgICAgICB9KSlcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZT1hd2FpdCBhcGlDbGllbnQucG9zdCgnL2F1dGgvbG9naW4nLHtcclxuICAgICAgICAgICAgICAgIGVtYWlsLHBhc3N3b3JkXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiTG9naW4gcmVzcG9uc2U6XCIsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgY29uc3QgeyB1c2VyLCB0b2tlbiB9ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xyXG5cclxuICAgICAgICAvLyBTdG9yZSBhdXRoIGRhdGEgaW4gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Rva2VuJywgdG9rZW4pO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VySWQnLCB1c2VyLmlkKTtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbmFtZScsIHVzZXIubmFtZSk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2VtYWlsJywgdXNlci5lbWFpbCk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Bob25lJywgdXNlci5waG9uZSB8fCAnJyk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2lzQWRtaW4nLCB1c2VyLmlzQWRtaW4gPyAndHJ1ZScgOiAnZmFsc2UnKTtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY3JlYXRlZEF0JywgdXNlci5jcmVhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpKTtcclxuXHJcbiAgICAgICAgLy8gVXBkYXRlIGF1dGggc3RhdGVcclxuICAgICAgICBzZXRBdXRoU3RhdGUoe1xyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogdXNlci5pZCxcclxuICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgICAgcGhvbmU6IHVzZXIucGhvbmUgfHwgJycsXHJcbiAgICAgICAgICAgIGlzQWRtaW46IHVzZXIuaXNBZG1pbixcclxuICAgICAgICAgICAgY3JlYXRlZEF0OiB1c2VyLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxyXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcclxuICAgICAgICAgIGVycm9yOiBudWxsLFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICB0b2FzdC5zdWNjZXNzKCdMb2dnZWQgaW4gc3VjY2Vzc2Z1bGx5IScpO1xyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFkbWluXHJcbiAgICAgICAgLy8gaWYgKHVzZXIuaXNBZG1pbikge1xyXG4gICAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcclxuICAgICAgICAvLyB9IGVsc2Uge1xyXG4gICAgICAgIC8vICAgdGhyb3cgbmV3IEVycm9yKCdBY2Nlc3MgZGVuaWVkLiBBZG1pbiBwcml2aWxlZ2VzIHJlcXVpcmVkLicpO1xyXG4gICAgICAgIC8vIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvZ2luIGZhaWxlZCcpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogdW5rbm93bikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ludmFsaWQgZW1haWwgb3IgcGFzc3dvcmQnO1xyXG4gICAgICBzZXRBdXRoU3RhdGUoKHByZXYpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UsXHJcbiAgICAgIH0pKTtcclxuICAgICAgdG9hc3QuZXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgIH1cclxuICB9O1xyXG4gIC8vIExvZ291dCBmdW5jdGlvblxyXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcclxuICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZVxyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcklkJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnbmFtZScpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2VtYWlsJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncGhvbmUnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdpc0FkbWluJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY3JlYXRlZEF0Jyk7XHJcblxyXG4gICAgLy8gUmVzZXQgYXV0aCBzdGF0ZVxyXG4gICAgc2V0QXV0aFN0YXRlKGluaXRpYWxTdGF0ZSk7XHJcblxyXG4gICAgLy8gUmVkaXJlY3QgdG8gbG9naW5cclxuICAgIHJvdXRlci5wdXNoKCcvYXV0aC9sb2dpbicpO1xyXG5cclxuICAgIHRvYXN0LnN1Y2Nlc3MoJ0xvZ2dlZCBvdXQgc3VjY2Vzc2Z1bGx5IScpO1xyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIGRpcmVjdGx5IHNldCBhdXRoIHN0YXRlICh1c2VkIGJ5IGF1dGgtY2FsbGJhY2spXHJcbiAgY29uc3Qgc2V0QXV0aFN0YXRlRGlyZWN0bHkgPSAoc3RhdGU6IEF1dGhTdGF0ZSkgPT4ge1xyXG4gICAgc2V0QXV0aFN0YXRlKHN0YXRlKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyXHJcbiAgICAgIHZhbHVlPXt7XHJcbiAgICAgICAgLi4uYXV0aFN0YXRlLFxyXG4gICAgICAgIGxvZ2luLFxyXG4gICAgICAgIGxvZ291dCxcclxuICAgICAgICBzZXRBdXRoU3RhdGU6IHNldEF1dGhTdGF0ZURpcmVjdGx5LFxyXG4gICAgICB9fVxyXG4gICAgPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn07XHJcbi8vIEN1c3RvbSBob29rIHRvIHVzZSBhdXRoIGNvbnRleHRcclxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKTogQXV0aENvbnRleHRUeXBlID0+IHtcclxuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XHJcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59O1xyXG5cclxuXHJcblxyXG5cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ29udGV4dCIsInVzZVJvdXRlciIsInRvYXN0IiwiYXBpQ2xpZW50IiwiaW5pdGlhbFN0YXRlIiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsImVycm9yIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImF1dGhTdGF0ZSIsInNldEF1dGhTdGF0ZSIsInJvdXRlciIsImNoZWNrQXV0aCIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXJJZCIsIm5hbWUiLCJlbWFpbCIsInBob25lIiwiaXNBZG1pbiIsImNyZWF0ZWRBdCIsImlkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY29uc29sZSIsImxvZyIsImxvZ2luIiwicGFzc3dvcmQiLCJwcmV2IiwicmVzcG9uc2UiLCJwb3N0IiwiZGF0YSIsInN0YXR1cyIsInNldEl0ZW0iLCJzdWNjZXNzIiwicHVzaCIsIkVycm9yIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsImxvZ291dCIsInJlbW92ZUl0ZW0iLCJzZXRBdXRoU3RhdGVEaXJlY3RseSIsInN0YXRlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUF1dGgiLCJjb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/apiClient.ts":
/*!**********************************!*\
  !*** ./src/lib/api/apiClient.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/orderService.ts":
/*!*************************************!*\
  !*** ./src/lib/api/orderService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orderService: () => (/* binding */ orderService)\n/* harmony export */ });\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n\nclass OrderService {\n    /**\r\n   * Get all orders with optional filters\r\n   */ async getOrders(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.status) {\n                params.append('status', filters.status);\n            }\n            if (filters.dateFilter) {\n                params.append('dateFilter', filters.dateFilter);\n            }\n            if (filters.user) {\n                params.append('user', filters.user);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `/orders${queryString ? `?${queryString}` : ''}`;\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching orders:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get a single order by ID\r\n   */ async getOrder(id) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/orders/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching order:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update order status\r\n   */ async updateOrderStatus(id, status) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/orders/${id}`, {\n                status\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error updating order status:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update order with comprehensive data\r\n   */ async updateOrder(id, orderData) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/orders/${id}`, orderData);\n            return response.data;\n        } catch (error) {\n            console.error('Error updating order:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Delete an order\r\n   */ async deleteOrder(id) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for consistent authorization handling\n            const response = await fetch(`/api/orders/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete order'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Order deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting order:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get order statistics\r\n   */ async getOrderStats() {\n        try {\n            // This would typically be a separate endpoint, but for now we'll calculate from all orders\n            const response = await this.getOrders({\n                limit: 1000\n            }); // Get a large number to calculate stats\n            const orders = response.data.orders;\n            const stats = {\n                totalOrders: orders.length,\n                pendingOrders: orders.filter((order)=>order.status === 'pending').length,\n                processingOrders: orders.filter((order)=>order.status === 'processing').length,\n                shippedOrders: orders.filter((order)=>order.status === 'shipped').length,\n                deliveredOrders: orders.filter((order)=>order.status === 'delivered').length,\n                cancelledOrders: orders.filter((order)=>order.status === 'cancelled').length,\n                totalRevenue: orders.reduce((sum, order)=>sum + order.total, 0)\n            };\n            return stats;\n        } catch (error) {\n            console.error('Error fetching order stats:', error);\n            throw error;\n        }\n    }\n}\nconst orderService = new OrderService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (orderService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9vcmRlclNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1EO0FBR25ELE1BQU1DO0lBQ0o7O0dBRUMsR0FDRCxNQUFNQyxVQUFVQyxVQUF3QixDQUFDLENBQUMsRUFBMkI7UUFDbkUsSUFBSTtZQUNGLE1BQU1DLFNBQVMsSUFBSUM7WUFFbkIsSUFBSUYsUUFBUUcsTUFBTSxFQUFFO2dCQUNsQkYsT0FBT0csTUFBTSxDQUFDLFVBQVVKLFFBQVFHLE1BQU07WUFDeEM7WUFDQSxJQUFJSCxRQUFRSyxNQUFNLEVBQUU7Z0JBQ2xCSixPQUFPRyxNQUFNLENBQUMsVUFBVUosUUFBUUssTUFBTTtZQUN4QztZQUNBLElBQUlMLFFBQVFNLFVBQVUsRUFBRTtnQkFDdEJMLE9BQU9HLE1BQU0sQ0FBQyxjQUFjSixRQUFRTSxVQUFVO1lBQ2hEO1lBQ0EsSUFBSU4sUUFBUU8sSUFBSSxFQUFFO2dCQUNoQk4sT0FBT0csTUFBTSxDQUFDLFFBQVFKLFFBQVFPLElBQUk7WUFDcEM7WUFDQSxJQUFJUCxRQUFRUSxJQUFJLEVBQUU7Z0JBQ2hCUCxPQUFPRyxNQUFNLENBQUMsUUFBUUosUUFBUVEsSUFBSSxDQUFDQyxRQUFRO1lBQzdDO1lBQ0EsSUFBSVQsUUFBUVUsS0FBSyxFQUFFO2dCQUNqQlQsT0FBT0csTUFBTSxDQUFDLFNBQVNKLFFBQVFVLEtBQUssQ0FBQ0QsUUFBUTtZQUMvQztZQUVBLE1BQU1FLGNBQWNWLE9BQU9RLFFBQVE7WUFDbkMsTUFBTUcsTUFBTSxDQUFDLE9BQU8sRUFBRUQsY0FBYyxDQUFDLENBQUMsRUFBRUEsYUFBYSxHQUFHLElBQUk7WUFFNUQsTUFBTUUsV0FBVyxNQUFNaEIsMERBQVNBLENBQUNpQixHQUFHLENBQWlCRjtZQUNyRCxPQUFPQyxTQUFTRSxJQUFJO1FBQ3RCLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1FLFNBQVNDLEVBQVUsRUFBdUQ7UUFDOUUsSUFBSTtZQUNGLE1BQU1OLFdBQVcsTUFBTWhCLDBEQUFTQSxDQUFDaUIsR0FBRyxDQUE2QyxDQUFDLFFBQVEsRUFBRUssSUFBSTtZQUNoRyxPQUFPTixTQUFTRSxJQUFJO1FBQ3RCLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1JLGtCQUFrQkQsRUFBVSxFQUFFZCxNQUFjLEVBQXVEO1FBQ3ZHLElBQUk7WUFDRixNQUFNUSxXQUFXLE1BQU1oQiwwREFBU0EsQ0FBQ3dCLEtBQUssQ0FBNkMsQ0FBQyxRQUFRLEVBQUVGLElBQUksRUFBRTtnQkFBRWQ7WUFBTztZQUM3RyxPQUFPUSxTQUFTRSxJQUFJO1FBQ3RCLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1NLFlBQVlILEVBQVUsRUFBRUksU0FBeUIsRUFBdUQ7UUFDNUcsSUFBSTtZQUNGLE1BQU1WLFdBQVcsTUFBTWhCLDBEQUFTQSxDQUFDd0IsS0FBSyxDQUE2QyxDQUFDLFFBQVEsRUFBRUYsSUFBSSxFQUFFSTtZQUNwRyxPQUFPVixTQUFTRSxJQUFJO1FBQ3RCLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1RLFlBQVlMLEVBQVUsRUFBZ0Q7UUFDMUUsSUFBSTtZQUNGLGlEQUFpRDtZQUNqRCxNQUFNTSxRQUFRLE1BQTZCLEdBQUdDLENBQTZCLEdBQUc7WUFFOUUsTUFBTUUsVUFBdUI7Z0JBQzNCLGdCQUFnQjtZQUNsQjtZQUVBLElBQUlILE9BQU87Z0JBQ1RHLE9BQU8sQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLE9BQU8sRUFBRUgsT0FBTztZQUM5QztZQUVBLDJEQUEyRDtZQUMzRCxNQUFNWixXQUFXLE1BQU1nQixNQUFNLENBQUMsWUFBWSxFQUFFVixJQUFJLEVBQUU7Z0JBQ2hEVyxRQUFRO2dCQUNSRjtZQUNGO1lBRUEsSUFBSSxDQUFDZixTQUFTa0IsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1uQixTQUFTb0IsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTzt3QkFBRUMsU0FBUztvQkFBeUI7Z0JBQ3pGLE1BQU0sSUFBSUMsTUFBTUosVUFBVUcsT0FBTyxJQUFJLENBQUMsb0JBQW9CLEVBQUV0QixTQUFTUixNQUFNLEVBQUU7WUFDL0U7WUFFQSxpQ0FBaUM7WUFDakMsSUFBSVEsU0FBU1IsTUFBTSxLQUFLLEtBQUs7Z0JBQzNCLE9BQU87b0JBQUVBLFFBQVE7b0JBQVc4QixTQUFTO2dCQUE2QjtZQUNwRTtZQUVBLE1BQU1wQixPQUFPLE1BQU1GLFNBQVNvQixJQUFJO1lBQ2hDLE9BQU9sQjtRQUNULEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1xQixnQkFRSDtRQUNELElBQUk7WUFDRiwyRkFBMkY7WUFDM0YsTUFBTXhCLFdBQVcsTUFBTSxJQUFJLENBQUNkLFNBQVMsQ0FBQztnQkFBRVcsT0FBTztZQUFLLElBQUksd0NBQXdDO1lBQ2hHLE1BQU00QixTQUFTekIsU0FBU0UsSUFBSSxDQUFDdUIsTUFBTTtZQUVuQyxNQUFNQyxRQUFRO2dCQUNaQyxhQUFhRixPQUFPRyxNQUFNO2dCQUMxQkMsZUFBZUosT0FBT0ssTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNdkMsTUFBTSxLQUFLLFdBQVdvQyxNQUFNO2dCQUN4RUksa0JBQWtCUCxPQUFPSyxNQUFNLENBQUNDLENBQUFBLFFBQVNBLE1BQU12QyxNQUFNLEtBQUssY0FBY29DLE1BQU07Z0JBQzlFSyxlQUFlUixPQUFPSyxNQUFNLENBQUNDLENBQUFBLFFBQVNBLE1BQU12QyxNQUFNLEtBQUssV0FBV29DLE1BQU07Z0JBQ3hFTSxpQkFBaUJULE9BQU9LLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTXZDLE1BQU0sS0FBSyxhQUFhb0MsTUFBTTtnQkFDNUVPLGlCQUFpQlYsT0FBT0ssTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNdkMsTUFBTSxLQUFLLGFBQWFvQyxNQUFNO2dCQUM1RVEsY0FBY1gsT0FBT1ksTUFBTSxDQUFDLENBQUNDLEtBQUtQLFFBQVVPLE1BQU1QLE1BQU1RLEtBQUssRUFBRTtZQUNqRTtZQUVBLE9BQU9iO1FBQ1QsRUFBRSxPQUFPdkIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3QyxNQUFNQTtRQUNSO0lBQ0Y7QUFDRjtBQUVPLE1BQU1xQyxlQUFlLElBQUl2RCxlQUFlO0FBQy9DLGlFQUFldUQsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGxpYlxcYXBpXFxvcmRlclNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFwaUNsaWVudCBmcm9tICcuLi8uLi8uLi9saWIvYXBpL2FwaUNsaWVudCc7XHJcbmltcG9ydCB7IE9yZGVyLCBPcmRlcnNSZXNwb25zZSwgT3JkZXJGaWx0ZXJzIH0gZnJvbSAnQC90eXBlcy9vcmRlcic7XHJcblxyXG5jbGFzcyBPcmRlclNlcnZpY2Uge1xyXG4gIC8qKlxyXG4gICAqIEdldCBhbGwgb3JkZXJzIHdpdGggb3B0aW9uYWwgZmlsdGVyc1xyXG4gICAqL1xyXG4gIGFzeW5jIGdldE9yZGVycyhmaWx0ZXJzOiBPcmRlckZpbHRlcnMgPSB7fSk6IFByb21pc2U8T3JkZXJzUmVzcG9uc2U+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcclxuICAgICAgXHJcbiAgICAgIGlmIChmaWx0ZXJzLnNlYXJjaCkge1xyXG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIGZpbHRlcnMuc2VhcmNoKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoZmlsdGVycy5zdGF0dXMpIHtcclxuICAgICAgICBwYXJhbXMuYXBwZW5kKCdzdGF0dXMnLCBmaWx0ZXJzLnN0YXR1cyk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKGZpbHRlcnMuZGF0ZUZpbHRlcikge1xyXG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ2RhdGVGaWx0ZXInLCBmaWx0ZXJzLmRhdGVGaWx0ZXIpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChmaWx0ZXJzLnVzZXIpIHtcclxuICAgICAgICBwYXJhbXMuYXBwZW5kKCd1c2VyJywgZmlsdGVycy51c2VyKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoZmlsdGVycy5wYWdlKSB7XHJcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgncGFnZScsIGZpbHRlcnMucGFnZS50b1N0cmluZygpKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoZmlsdGVycy5saW1pdCkge1xyXG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ2xpbWl0JywgZmlsdGVycy5saW1pdC50b1N0cmluZygpKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcXVlcnlTdHJpbmcgPSBwYXJhbXMudG9TdHJpbmcoKTtcclxuICAgICAgY29uc3QgdXJsID0gYC9vcmRlcnMke3F1ZXJ5U3RyaW5nID8gYD8ke3F1ZXJ5U3RyaW5nfWAgOiAnJ31gO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0PE9yZGVyc1Jlc3BvbnNlPih1cmwpO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG9yZGVyczonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGEgc2luZ2xlIG9yZGVyIGJ5IElEXHJcbiAgICovXHJcbiAgYXN5bmMgZ2V0T3JkZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogeyBvcmRlcjogT3JkZXIgfSB9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQ8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogeyBvcmRlcjogT3JkZXIgfSB9PihgL29yZGVycy8ke2lkfWApO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG9yZGVyOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBVcGRhdGUgb3JkZXIgc3RhdHVzXHJcbiAgICovXHJcbiAgYXN5bmMgdXBkYXRlT3JkZXJTdGF0dXMoaWQ6IHN0cmluZywgc3RhdHVzOiBzdHJpbmcpOiBQcm9taXNlPHsgc3RhdHVzOiBzdHJpbmc7IGRhdGE6IHsgb3JkZXI6IE9yZGVyIH0gfT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucGF0Y2g8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogeyBvcmRlcjogT3JkZXIgfSB9PihgL29yZGVycy8ke2lkfWAsIHsgc3RhdHVzIH0pO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIG9yZGVyIHN0YXR1czonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVXBkYXRlIG9yZGVyIHdpdGggY29tcHJlaGVuc2l2ZSBkYXRhXHJcbiAgICovXHJcbiAgYXN5bmMgdXBkYXRlT3JkZXIoaWQ6IHN0cmluZywgb3JkZXJEYXRhOiBQYXJ0aWFsPE9yZGVyPik6IFByb21pc2U8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogeyBvcmRlcjogT3JkZXIgfSB9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wYXRjaDx7IHN0YXR1czogc3RyaW5nOyBkYXRhOiB7IG9yZGVyOiBPcmRlciB9IH0+KGAvb3JkZXJzLyR7aWR9YCwgb3JkZXJEYXRhKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBvcmRlcjonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRGVsZXRlIGFuIG9yZGVyXHJcbiAgICovXHJcbiAgYXN5bmMgZGVsZXRlT3JkZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8eyBzdGF0dXM6IHN0cmluZzsgbWVzc2FnZTogc3RyaW5nIH0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSBmb3IgYXV0aGVudGljYXRpb25cclxuICAgICAgY29uc3QgdG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIDogbnVsbDtcclxuXHJcbiAgICAgIGNvbnN0IGhlYWRlcnM6IEhlYWRlcnNJbml0ID0ge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICBpZiAodG9rZW4pIHtcclxuICAgICAgICBoZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7dG9rZW59YDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVXNlIGZldGNoIGRpcmVjdGx5IGZvciBjb25zaXN0ZW50IGF1dGhvcml6YXRpb24gaGFuZGxpbmdcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9vcmRlcnMvJHtpZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgICBoZWFkZXJzLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHsgbWVzc2FnZTogJ0ZhaWxlZCB0byBkZWxldGUgb3JkZXInIH0pKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEhhbmRsZSAyMDQgTm8gQ29udGVudCByZXNwb25zZVxyXG4gICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSAyMDQpIHtcclxuICAgICAgICByZXR1cm4geyBzdGF0dXM6ICdzdWNjZXNzJywgbWVzc2FnZTogJ09yZGVyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICByZXR1cm4gZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIG9yZGVyOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgb3JkZXIgc3RhdGlzdGljc1xyXG4gICAqL1xyXG4gIGFzeW5jIGdldE9yZGVyU3RhdHMoKTogUHJvbWlzZTx7XHJcbiAgICB0b3RhbE9yZGVyczogbnVtYmVyO1xyXG4gICAgdG90YWxSZXZlbnVlOiBudW1iZXI7XHJcbiAgICBwZW5kaW5nT3JkZXJzOiBudW1iZXI7XHJcbiAgICBwcm9jZXNzaW5nT3JkZXJzOiBudW1iZXI7XHJcbiAgICBzaGlwcGVkT3JkZXJzOiBudW1iZXI7XHJcbiAgICBkZWxpdmVyZWRPcmRlcnM6IG51bWJlcjtcclxuICAgIGNhbmNlbGxlZE9yZGVyczogbnVtYmVyO1xyXG4gIH0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFRoaXMgd291bGQgdHlwaWNhbGx5IGJlIGEgc2VwYXJhdGUgZW5kcG9pbnQsIGJ1dCBmb3Igbm93IHdlJ2xsIGNhbGN1bGF0ZSBmcm9tIGFsbCBvcmRlcnNcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldE9yZGVycyh7IGxpbWl0OiAxMDAwIH0pOyAvLyBHZXQgYSBsYXJnZSBudW1iZXIgdG8gY2FsY3VsYXRlIHN0YXRzXHJcbiAgICAgIGNvbnN0IG9yZGVycyA9IHJlc3BvbnNlLmRhdGEub3JkZXJzO1xyXG4gICAgICBcclxuICAgICAgY29uc3Qgc3RhdHMgPSB7XHJcbiAgICAgICAgdG90YWxPcmRlcnM6IG9yZGVycy5sZW5ndGgsXHJcbiAgICAgICAgcGVuZGluZ09yZGVyczogb3JkZXJzLmZpbHRlcihvcmRlciA9PiBvcmRlci5zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RoLFxyXG4gICAgICAgIHByb2Nlc3NpbmdPcmRlcnM6IG9yZGVycy5maWx0ZXIob3JkZXIgPT4gb3JkZXIuc3RhdHVzID09PSAncHJvY2Vzc2luZycpLmxlbmd0aCxcclxuICAgICAgICBzaGlwcGVkT3JkZXJzOiBvcmRlcnMuZmlsdGVyKG9yZGVyID0+IG9yZGVyLnN0YXR1cyA9PT0gJ3NoaXBwZWQnKS5sZW5ndGgsXHJcbiAgICAgICAgZGVsaXZlcmVkT3JkZXJzOiBvcmRlcnMuZmlsdGVyKG9yZGVyID0+IG9yZGVyLnN0YXR1cyA9PT0gJ2RlbGl2ZXJlZCcpLmxlbmd0aCxcclxuICAgICAgICBjYW5jZWxsZWRPcmRlcnM6IG9yZGVycy5maWx0ZXIob3JkZXIgPT4gb3JkZXIuc3RhdHVzID09PSAnY2FuY2VsbGVkJykubGVuZ3RoLFxyXG4gICAgICAgIHRvdGFsUmV2ZW51ZTogb3JkZXJzLnJlZHVjZSgoc3VtLCBvcmRlcikgPT4gc3VtICsgb3JkZXIudG90YWwsIDApLFxyXG4gICAgICB9O1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHN0YXRzO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgb3JkZXIgc3RhdHM6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBvcmRlclNlcnZpY2UgPSBuZXcgT3JkZXJTZXJ2aWNlKCk7XHJcbmV4cG9ydCBkZWZhdWx0IG9yZGVyU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImFwaUNsaWVudCIsIk9yZGVyU2VydmljZSIsImdldE9yZGVycyIsImZpbHRlcnMiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJzZWFyY2giLCJhcHBlbmQiLCJzdGF0dXMiLCJkYXRlRmlsdGVyIiwidXNlciIsInBhZ2UiLCJ0b1N0cmluZyIsImxpbWl0IiwicXVlcnlTdHJpbmciLCJ1cmwiLCJyZXNwb25zZSIsImdldCIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJnZXRPcmRlciIsImlkIiwidXBkYXRlT3JkZXJTdGF0dXMiLCJwYXRjaCIsInVwZGF0ZU9yZGVyIiwib3JkZXJEYXRhIiwiZGVsZXRlT3JkZXIiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoZWFkZXJzIiwiZmV0Y2giLCJtZXRob2QiLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsIm1lc3NhZ2UiLCJFcnJvciIsImdldE9yZGVyU3RhdHMiLCJvcmRlcnMiLCJzdGF0cyIsInRvdGFsT3JkZXJzIiwibGVuZ3RoIiwicGVuZGluZ09yZGVycyIsImZpbHRlciIsIm9yZGVyIiwicHJvY2Vzc2luZ09yZGVycyIsInNoaXBwZWRPcmRlcnMiLCJkZWxpdmVyZWRPcmRlcnMiLCJjYW5jZWxsZWRPcmRlcnMiLCJ0b3RhbFJldmVudWUiLCJyZWR1Y2UiLCJzdW0iLCJ0b3RhbCIsIm9yZGVyU2VydmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/orderService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/productService.ts":
/*!***************************************!*\
  !*** ./src/lib/api/productService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n\nclass ProductService {\n    /**\n   * Get all products with optional filters\n   */ async getProducts(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            // Add filters to query params\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.category) {\n                params.append('category', filters.category);\n            }\n            if (filters.type) {\n                params.append('type', filters.type);\n            }\n            if (filters.minPrice !== undefined) {\n                params.append('price[gte]', filters.minPrice.toString());\n            }\n            if (filters.maxPrice !== undefined) {\n                params.append('price[lte]', filters.maxPrice.toString());\n            }\n            if (filters.inStock !== undefined) {\n                params.append('stock', filters.inStock.toString());\n            }\n            if (filters.sort) {\n                params.append('sort', filters.sort);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `/products${queryString ? `?${queryString}` : ''}`;\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get a single product by ID\n   */ async getProduct(id) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/products/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new product with FormData (for file uploads)\n   */ async createProduct(formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            // Use Next.js API proxy to avoid CORS issues\n            const response = await fetch('/api/products', {\n                method: 'POST',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to create product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error creating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update a product with FormData (for file uploads)\n   */ async updateProduct(id, formData) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {};\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for FormData to avoid axios content-type issues\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'PATCH',\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to update product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error updating product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a product\n   */ async deleteProduct(id) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for consistent authorization handling\n            const response = await fetch(`/api/products/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete product'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Product deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting product:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get product categories\n   */ async getCategories() {\n        try {\n            const response = await this.getProducts();\n            const categories = [\n                ...new Set(response.data.products.map((product)=>product.category))\n            ];\n            return categories.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            return [];\n        }\n    }\n}\nconst productService = new ProductService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (productService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/productService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/videoBlogService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/videoBlogService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass VideoBlogService {\n    /**\r\n   * Get all video blogs with optional filters\r\n   */ async getVideoBlogs(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            // Add filters to query params\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.category && filters.category !== 'all') {\n                params.append('category', filters.category);\n            }\n            if (filters.tag) {\n                params.append('tag', filters.tag);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `${this.baseURL}/video-blogs${queryString ? `?${queryString}` : ''}`;\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(url, {\n                headers\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            console.log('Video blogs API response:', data);\n            // Transform _id to id if needed\n            if (data.data && data.data.videoBlogs) {\n                data.data.videoBlogs = data.data.videoBlogs.map((videoBlog)=>({\n                        ...videoBlog,\n                        id: videoBlog.id || videoBlog._id\n                    }));\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching video blogs:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get a single video blog by ID\r\n   */ async getVideoBlog(id) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to fetch video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            console.log('Single video blog API response:', data);\n            // Transform _id to id if needed\n            if (data.data && data.data.videoBlog) {\n                data.data.videoBlog = {\n                    ...data.data.videoBlog,\n                    id: data.data.videoBlog.id || data.data.videoBlog._id\n                };\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Create a new video blog\r\n   */ async createVideoBlog(data) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs`, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to create video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error creating video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update a video blog\r\n   */ async updateVideoBlog(id, data) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                method: 'PATCH',\n                headers,\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to update video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error updating video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Delete a video blog\r\n   */ async deleteVideoBlog(id) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Video blog deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get video blog categories\r\n   */ async getCategories() {\n        try {\n            const response = await this.getVideoBlogs();\n            const categories = [\n                ...new Set(response.data.videoBlogs.map((blog)=>blog.category))\n            ];\n            return categories.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            return [\n                'General',\n                'Technology',\n                'Education',\n                'Entertainment',\n                'Business',\n                'Health',\n                'Sports'\n            ];\n        }\n    }\n    /**\r\n   * Get all unique tags\r\n   */ async getTags() {\n        try {\n            const response = await this.getVideoBlogs();\n            const allTags = response.data.videoBlogs.flatMap((blog)=>blog.tags);\n            const uniqueTags = [\n                ...new Set(allTags)\n            ];\n            return uniqueTags.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching tags:', error);\n            return [];\n        }\n    }\n    /**\r\n   * Extract YouTube video ID from URL\r\n   */ extractYouTubeVideoId(url) {\n        const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    /**\r\n   * Validate YouTube URL\r\n   */ isValidYouTubeUrl(url) {\n        const regex = /^https?:\\/\\/(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\n        return regex.test(url);\n    }\n    constructor(){\n        this.baseURL = '/api';\n    }\n}\nconst videoBlogService = new VideoBlogService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (videoBlogService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/videoBlogService.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n\nconst userService = {\n    // Get all users\n    async getUsers (page = 1, limit = 10, search = '') {\n        try {\n            const params = new URLSearchParams({\n                page: page.toString(),\n                limit: limit.toString(),\n                ...search && {\n                    search\n                }\n            });\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/users?${params}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            throw error;\n        }\n    },\n    // Get user by ID\n    async getUserById (id) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/users/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching user:', error);\n            throw error;\n        }\n    },\n    // Update user\n    async updateUser (id, userData) {\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/users/${id}`, userData);\n            return response.data;\n        } catch (error) {\n            console.error('Error updating user:', error);\n            throw error;\n        }\n    },\n    // Delete user\n    async deleteUser (id) {\n        try {\n            // Get token from localStorage for authentication\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            // Use fetch directly for consistent authorization handling\n            const response = await fetch(`/api/users/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete user'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'User deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            throw error;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/userService.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/sonner","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();