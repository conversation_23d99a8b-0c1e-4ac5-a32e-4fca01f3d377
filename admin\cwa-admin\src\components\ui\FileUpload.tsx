'use client';

import { useState, useRef, useCallback } from 'react';
import { CloudArrowUpIcon, XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';

interface FileUploadProps {
  onFileSelect: (file: File | null) => void;
  onMultipleFileSelect?: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
  currentFile?: File | null;
  currentImageUrl?: string;
  error?: string;
  className?: string;
  label?: string;
  hint?: string;
}

export default function FileUpload({
  onFileSelect,
  onMultipleFileSelect,
  accept = 'image/*',
  multiple = false,
  maxSize = 5,
  currentFile,
  currentImageUrl,
  error,
  className = '',
  label = 'Upload Image',
  hint = 'PNG, JPG, GIF up to 5MB'
}: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [preview, setPreview] = useState<string | null>(currentImageUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): string | null => {
    if (maxSize && file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`;
    }

    if (accept && !file.type.match(accept.replace('*', '.*'))) {
      return 'Invalid file type';
    }

    return null;
  }, [maxSize, accept]);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    if (multiple && onMultipleFileSelect) {
      const validFiles: File[] = [];
      const errors: string[] = [];
      
      Array.from(files).forEach(file => {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          validFiles.push(file);
        }
      });
      
      if (errors.length > 0) {
        console.error('File validation errors:', errors);
      }
      
      onMultipleFileSelect(validFiles);
    } else {
      const file = files[0];
      const validationError = validateFile(file);
      
      if (validationError) {
        console.error('File validation error:', validationError);
        onFileSelect(null);
        return;
      }
      
      onFileSelect(file);
      
      // Create preview for single file
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    }
  }, [multiple, onFileSelect, onMultipleFileSelect, validateFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const handleRemoveFile = () => {
    onFileSelect(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          isDragOver
            ? 'border-blue-400 bg-blue-50'
            : error
            ? 'border-red-300 bg-red-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          aria-describedby={hint ? 'file-upload-hint' : undefined}
        />

        {preview ? (
          // Preview mode
          <div className="relative">
            <img
              src={preview}
              alt="Preview"
              className="max-w-full max-h-48 mx-auto rounded-lg object-cover"
            />
            <button
              onClick={handleRemoveFile}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              aria-label="Remove image"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        ) : (
          // Upload mode
          <div className="text-center">
            <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <button
                type="button"
                onClick={openFileDialog}
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Click to upload
              </button>
              <span className="text-gray-500"> or drag and drop</span>
            </div>
            {hint && (
              <p id="file-upload-hint" className="mt-2 text-sm text-gray-500">
                {hint}
              </p>
            )}
          </div>
        )}

        {currentFile && !preview && (
          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3 mt-4">
            <div className="flex items-center">
              <PhotoIcon className="w-5 h-5 text-gray-400 mr-2" />
              <span className="text-sm text-gray-700 truncate">
                {currentFile.name}
              </span>
            </div>
            <button
              onClick={handleRemoveFile}
              className="text-red-500 hover:text-red-700 transition-colors"
              aria-label="Remove file"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
}

// Multiple file upload component
interface MultipleFileUploadProps {
  onFilesSelect: (files: File[]) => void;
  currentFiles?: File[];
  currentImageUrls?: string[];
  accept?: string;
  maxSize?: number;
  maxFiles?: number;
  error?: string;
  className?: string;
  label?: string;
  hint?: string;
}

export function MultipleFileUpload({
  onFilesSelect,
  currentFiles = [],
  currentImageUrls = [],
  accept = 'image/*',
  maxSize = 5,
  maxFiles = 5,
  error,
  className = '',
  label = 'Upload Additional Images',
  hint = `PNG, JPG, GIF up to ${maxSize}MB each (max ${maxFiles} files)`
}: MultipleFileUploadProps) {
  const [previews, setPreviews] = useState<string[]>(currentImageUrls);

  const handleFilesSelect = (newFiles: File[]) => {
    const totalFiles = currentFiles.length + newFiles.length;
    if (totalFiles > maxFiles) {
      console.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const updatedFiles = [...currentFiles, ...newFiles];
    onFilesSelect(updatedFiles);

    // Create previews for new files
    newFiles.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreviews(prev => [...prev, e.target?.result as string]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const removeFile = (index: number) => {
    const updatedFiles = currentFiles.filter((_, i) => i !== index);
    const updatedPreviews = previews.filter((_, i) => i !== index);
    
    onFilesSelect(updatedFiles);
    setPreviews(updatedPreviews);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <FileUpload
        onFileSelect={() => {}} // Required prop, but not used in multiple mode
        onMultipleFileSelect={handleFilesSelect}
        accept={accept}
        multiple
        maxSize={maxSize}
        error={error}
        label={label}
        hint={hint}
      />

      {/* File previews */}
      {previews.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {previews.map((preview, index) => (
            <div key={index} className="relative group">
              <img
                src={preview}
                alt={`Preview ${index + 1}`}
                className="w-full h-24 object-cover rounded-lg border"
              />
              <button
                onClick={() => removeFile(index)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                aria-label={`Remove image ${index + 1}`}
              >
                <XMarkIcon className="w-3 h-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
