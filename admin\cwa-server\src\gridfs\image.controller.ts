import {
  <PERSON>,
  Get,
  Param,
  Res,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { GridFSService } from './gridfs.service';
import { ObjectId } from 'mongodb';

@Controller('images')
export class ImageController {
  constructor(private readonly gridfsService: GridFSService) {}

  @Get('file/:filename')
  async getImageByFilename(
    @Param('filename') filename: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      console.log('Image request for filename:', filename);

      // Validate filename to prevent directory traversal attacks
      if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new HttpException(
          {
            status: 'fail',
            message: 'Invalid filename',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const file = await this.gridfsService.findFileByFilename(filename);

      if (!file) {
        throw new HttpException(
          {
            status: 'fail',
            message: 'Image not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      console.log('File found:', file.filename, 'mimetype:', file.metadata?.mimetype);

      // Set security headers
      res.set({
        'Content-Type': file.metadata?.mimetype || 'image/jpeg',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      });

      // Create a read stream and pipe it to the response
      const readStream = this.gridfsService.createReadStreamByFilename(filename);

      readStream.on('error', (error) => {
        console.error('GridFS read stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            status: 'error',
            message: 'Error streaming image'
          });
        }
      });

      readStream.pipe(res);

    } catch (error) {
      console.error('Error retrieving image:', error);
      if (!res.headersSent) {
        if (error instanceof HttpException) {
          res.status(error.getStatus()).json(error.getResponse());
        } else {
          res.status(500).json({
            status: 'error',
            message: 'Error retrieving image',
            error: error.message
          });
        }
      }
    }
  }

  @Get('id/:id')
  async getImageById(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      console.log('Image request for ID:', id);

      // Validate if the ID is a valid ObjectId
      if (!ObjectId.isValid(id)) {
        throw new HttpException(
          {
            status: 'fail',
            message: 'Invalid image ID',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const file = await this.gridfsService.findFileById(id);

      if (!file) {
        throw new HttpException(
          {
            status: 'fail',
            message: 'Image not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      console.log('File found by ID:', file.filename, 'mimetype:', file.metadata?.mimetype);

      // Set security headers
      res.set({
        'Content-Type': file.metadata?.mimetype || 'image/jpeg',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      });

      // Create a read stream and pipe it to the response
      const readStream = this.gridfsService.createReadStream(id);

      readStream.on('error', (error) => {
        console.error('GridFS read stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            status: 'error',
            message: 'Error streaming image'
          });
        }
      });

      readStream.pipe(res);

    } catch (error) {
      console.error('Error retrieving image:', error);
      if (!res.headersSent) {
        if (error instanceof HttpException) {
          res.status(error.getStatus()).json(error.getResponse());
        } else {
          res.status(500).json({
            status: 'error',
            message: 'Error retrieving image',
            error: error.message
          });
        }
      }
    }
  }

  @Get('debug/list-files')
  async listAllFiles(): Promise<any> {
    try {
      const files = await this.gridfsService.listAllFiles();
      return {
        status: 'success',
        count: files.length,
        files: files.map(file => ({
          filename: file.filename,
          length: file.length,
          uploadDate: file.uploadDate,
          metadata: file.metadata
        }))
      };
    } catch (error) {
      console.error('Error listing files:', error);
      throw new HttpException(
        {
          status: 'error',
          message: 'Error listing files',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
