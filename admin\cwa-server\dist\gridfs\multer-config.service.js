"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MulterConfigService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const multer_gridfs_storage_1 = require("multer-gridfs-storage");
const path = require("path");
const crypto = require("crypto");
let MulterConfigService = class MulterConfigService {
    constructor(configService) {
        this.configService = configService;
    }
    createGridFSStorage() {
        const mongoUri = this.configService.get('MONGODB_URI');
        if (!mongoUri) {
            throw new Error('MONGODB_URI environment variable is not set');
        }
        return new multer_gridfs_storage_1.GridFsStorage({
            url: mongoUri,
            options: { useNewUrlParser: true, useUnifiedTopology: true },
            file: (req, file) => {
                return new Promise((resolve, reject) => {
                    crypto.randomBytes(16, (err, buf) => {
                        if (err) {
                            return reject(err);
                        }
                        const fileInfo = {
                            filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
                            bucketName: 'uploads',
                            metadata: {
                                originalname: file.originalname,
                                mimetype: file.mimetype,
                                uploadDate: new Date()
                            }
                        };
                        resolve(fileInfo);
                    });
                });
            }
        });
    }
    getMulterOptions() {
        const storage = this.createGridFSStorage();
        const fileFilter = (req, file, cb) => {
            if (file.mimetype.startsWith('image/')) {
                cb(null, true);
            }
            else {
                cb(new Error('Not an image! Please upload only images.'), false);
            }
        };
        return {
            storage: storage,
            fileFilter: fileFilter,
            limits: {
                fileSize: 5 * 1024 * 1024
            }
        };
    }
};
exports.MulterConfigService = MulterConfigService;
exports.MulterConfigService = MulterConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MulterConfigService);
//# sourceMappingURL=multer-config.service.js.map