/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/images/[filename]/route";
exports.ids = ["app/api/images/[filename]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&page=%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&page=%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_images_filename_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/images/[filename]/route.ts */ \"(rsc)/./src/app/api/images/[filename]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/images/[filename]/route\",\n        pathname: \"/api/images/[filename]\",\n        filename: \"route\",\n        bundlePath: \"app/api/images/[filename]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\api\\\\images\\\\[filename]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_images_filename_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&page=%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/images/[filename]/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/images/[filename]/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst BACKEND_URL = \"http://localhost:4000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const { filename } = await params;\n        console.log('Image proxy request for:', filename);\n        console.log('Backend URL:', `${BACKEND_URL}/${filename}`);\n        // Validate filename to prevent directory traversal attacks\n        if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\\\')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid filename'\n            }, {\n                status: 400\n            });\n        }\n        // Check if filename has valid image extension\n        const imageExtensions = /\\.(jpg|jpeg|png|gif|webp|svg|ico)$/i;\n        if (!imageExtensions.test(filename)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid file type'\n            }, {\n                status: 400\n            });\n        }\n        // Fetch the image from the backend\n        const response = await fetch(`${BACKEND_URL}/${filename}`, {\n            method: 'GET',\n            headers: {\n                'Accept': 'image/*'\n            }\n        });\n        if (!response.ok) {\n            console.log('Image not found on backend:', filename);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Image not found'\n            }, {\n                status: 404\n            });\n        }\n        // Get the image data\n        const imageBuffer = await response.arrayBuffer();\n        const contentType = response.headers.get('content-type') || 'image/jpeg';\n        console.log('Successfully proxied image:', filename, 'Content-Type:', contentType);\n        // Return the image with proper headers\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(imageBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': contentType,\n                'Cache-Control': 'public, max-age=3600',\n                'X-Content-Type-Options': 'nosniff'\n            }\n        });\n    } catch (error) {\n        console.error('Error proxying image:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Error loading image'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/images/[filename]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&page=%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fimages%2F%5Bfilename%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();