import apiClient from '../../lib/api/apiClient';
import { User } from '../types/user';

export interface UsersResponse {
  status: string;
  data?: {
    users: User[];
    total: number;
    page: number;
    limit: number;
  };
  message?: string;
}

export const userService = {
  // Get all users
  async getUsers(page = 1, limit = 10, search = ''): Promise<UsersResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
      });

      const response = await apiClient.get(`/users?${params}`);
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  // Get user by ID
  async getUserById(id: string): Promise<User> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching user:', error);
      throw error;
    }
  },

  // Update user
  async updateUser(id: string, userData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put(`/users/${id}`, userData);
      return response.data;
    } catch (error: unknown) {
      console.error('Error updating user:', error);
      throw error;
    }
  },

  // Delete user
  async deleteUser(id: string): Promise<{ status: string; message: string }> {
    try {
      // Get token from localStorage for authentication
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use fetch directly for consistent authorization handling
      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete user' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      // Handle 204 No Content response
      if (response.status === 204) {
        return { status: 'success', message: 'User deleted successfully' };
      }

      const data = await response.json();
      return data;
    } catch (error: unknown) {
      console.error('Error deleting user:', error);
      throw error;
    }
  },
};

