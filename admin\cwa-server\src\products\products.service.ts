import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product } from './schema/product.schema';
import { IProduct, IProductQuery } from './interfaces/product.interface';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<IProduct>,
  ) {}

  async createProduct(createProductDto: CreateProductDto): Promise<IProduct> {
    try {
      const productData = {
        ...createProductDto,
        stock: createProductDto.stock !== undefined ? createProductDto.stock : true,
        type: createProductDto.type || 'regular',
      };

      const product = new this.productModel(productData);
      return await product.save();
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('Product with this name already exists');
      }
      throw new BadRequestException(error.message);
    }
  }

  async findAll(query: IProductQuery = {}): Promise<{ products: IProduct[]; results: number }> {
    try {
      // Build query object
      const queryObj: any = {};
      
      // Filter by category
      if (query.category) {
        queryObj.category = query.category;
      }
      
      // Filter by type
      if (query.type) {
        queryObj.type = query.type;
      }
      
      // Filter by stock
      if (query.stock !== undefined) {
        queryObj.stock = query.stock;
      }
      
      // Price filtering
      if (query.price) {
        queryObj.price = {};
        if (query.price.gte !== undefined) queryObj.price.$gte = query.price.gte;
        if (query.price.lte !== undefined) queryObj.price.$lte = query.price.lte;
        if (query.price.gt !== undefined) queryObj.price.$gt = query.price.gt;
        if (query.price.lt !== undefined) queryObj.price.$lt = query.price.lt;
      }

      let mongoQuery = this.productModel.find(queryObj);

      // Sorting
      if (query.sort) {
        const sortBy = query.sort.split(',').join(' ');
        mongoQuery = mongoQuery.sort(sortBy);
      } else {
        mongoQuery = mongoQuery.sort('-createdAt');
      }

      // Field limiting
      if (query.fields) {
        const fields = query.fields.split(',').join(' ');
        mongoQuery = mongoQuery.select(fields);
      } else {
        mongoQuery = mongoQuery.select('-__v');
      }

      // Pagination
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      mongoQuery = mongoQuery.skip(skip).limit(limit);

      const products = await mongoQuery.exec();
      const results = products.length;

      return { products, results };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async findById(id: string): Promise<IProduct> {
    try {
      const product = await this.productModel.findById(id).select('-__v').exec();
      
      if (!product) {
        throw new NotFoundException('Product not found');
      }
      
      return product;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Invalid product ID');
    }
  }

  async updateProduct(id: string, updateProductDto: UpdateProductDto): Promise<IProduct> {
    try {
      const product = await this.productModel.findByIdAndUpdate(
        id,
        updateProductDto,
        {
          new: true,
          runValidators: true,
        }
      ).select('-__v').exec();

      if (!product) {
        throw new NotFoundException('Product not found');
      }

      return product;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  async deleteProduct(id: string): Promise<void> {
    try {
      const result = await this.productModel.findByIdAndDelete(id).exec();
      
      if (!result) {
        throw new NotFoundException('Product not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Invalid product ID');
    }
  }
}
