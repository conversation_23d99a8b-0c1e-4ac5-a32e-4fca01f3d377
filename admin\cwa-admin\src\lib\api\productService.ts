import apiClient from '../../../lib/api/apiClient';
import { Product, ProductsResponse } from '@/types/user';

export interface ProductFilters {
  search?: string;
  category?: string;
  type?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  sort?: string;
  page?: number;
  limit?: number;
}

class ProductService {
  /**
   * Get all products with optional filters
   */
  async getProducts(filters: ProductFilters = {}): Promise<ProductsResponse> {
    try {
      const params = new URLSearchParams();
      
      // Add filters to query params
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.category) {
        params.append('category', filters.category);
      }
      if (filters.type) {
        params.append('type', filters.type);
      }
      if (filters.minPrice !== undefined) {
        params.append('price[gte]', filters.minPrice.toString());
      }
      if (filters.maxPrice !== undefined) {
        params.append('price[lte]', filters.maxPrice.toString());
      }
      if (filters.inStock !== undefined) {
        params.append('stock', filters.inStock.toString());
      }
      if (filters.sort) {
        params.append('sort', filters.sort);
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString());
      }

      const queryString = params.toString();
      const url = `/products${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiClient.get<ProductsResponse>(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  /**
   * Get a single product by ID
   */
  async getProduct(id: string): Promise<{ status: string; data: Product }> {
    try {
      const response = await apiClient.get<{ status: string; data: Product }>(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  /**
   * Create a new product with FormData (for file uploads)
   */
  async createProduct(formData: FormData): Promise<{ status: string; data: { product: Product } }> {
    try {
      // Get token from localStorage for authentication
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use fetch directly for FormData to avoid axios content-type issues
      // Use Next.js API proxy to avoid CORS issues
      const response = await fetch('/api/products', {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to create product' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  /**
   * Update a product with FormData (for file uploads)
   */
  async updateProduct(id: string, formData: FormData): Promise<{ status: string; data: { product: Product } }> {
    try {
      // Get token from localStorage for authentication
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use fetch directly for FormData to avoid axios content-type issues
      const response = await fetch(`/api/products/${id}`, {
        method: 'PATCH',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to update product' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string): Promise<{ status: string; message: string }> {
    try {
      // Get token from localStorage for authentication
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use fetch directly for consistent authorization handling
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete product' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      // Handle 204 No Content response
      if (response.status === 204) {
        return { status: 'success', message: 'Product deleted successfully' };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  /**
   * Get product categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const response = await this.getProducts();
      const categories = [...new Set(response.data.products.map(product => product.category))];
      return categories.filter(Boolean);
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }
}

const productService = new ProductService();
export default productService;
