"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("@nestjs/mongoose");
const users_schema_1 = require("../users/schema/users.schema");
const users_module_1 = require("../users/users.module");
const auth_guard_1 = require("./auth.guard");
const auth_service_1 = require("./auth.service");
const auth_controller_1 = require("./auth.controller");
const otp_schema_1 = require("../otp/schema/otp.schema");
const otp_service_1 = require("../otp/otp.service");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            mongoose_1.MongooseModule.forFeature([
                { name: users_schema_1.Users.name, schema: users_schema_1.UsersSchema },
                { name: otp_schema_1.Otp.name, schema: otp_schema_1.OtpSchema },
            ]),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: { expiresIn: '100y' },
                }),
                inject: [config_1.ConfigService],
            })
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [auth_service_1.AuthService, auth_guard_1.AuthGuard, otp_service_1.OtpService],
        exports: [auth_service_1.AuthService, auth_guard_1.AuthGuard, jwt_1.JwtModule, otp_service_1.OtpService],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map