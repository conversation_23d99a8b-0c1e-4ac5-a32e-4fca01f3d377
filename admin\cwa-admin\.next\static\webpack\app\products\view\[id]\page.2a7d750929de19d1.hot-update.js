"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/view/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/**\r\n * Format a date to a readable string\r\n */ function formatDate(date) {\n    try {\n        const dateObj = date instanceof Date ? date : new Date(date);\n        // Check if the date is valid\n        if (isNaN(dateObj.getTime())) {\n            return 'Invalid Date';\n        }\n        return new Intl.DateTimeFormat('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        }).format(dateObj);\n    } catch (error) {\n        console.error('Error formatting date:', error);\n        return 'Invalid Date';\n    }\n}\n/**\r\n * Format a number as currency\r\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(amount);\n}\n/**\r\n * Truncate a string to a specified length\r\n */ function truncateString(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + '...';\n}\n/**\r\n * Generate a random ID\r\n */ function generateId() {\n    return Math.random().toString(36).substring(2, 9);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});