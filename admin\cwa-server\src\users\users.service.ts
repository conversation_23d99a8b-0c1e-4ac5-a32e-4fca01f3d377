import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { Users } from './schema/users.schema';
import {
  IUser,
  IUserResponse,
  IUserQuery,
  IUserPaginationResponse
} from './interfaces/users.interface';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(Users.name) private userModel: Model<IUser>,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<IUserResponse> {
    try {
      // Check if user already exists
      const existingUser = await this.userModel.findOne({
        email: createUserDto.email.toLowerCase()
      });

      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }

      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

      // Prepare user data
      const userData = {
        ...createUserDto,
        email: createUserDto.email.toLowerCase(),
        password: hashedPassword,
        isAdmin: createUserDto.isAdmin || false,
      };

      // Create user
      const user = new this.userModel(userData);
      const savedUser = await user.save();

      // Return user without password
      const { password, ...userWithoutPassword } = savedUser.toObject();
      return userWithoutPassword ;
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }

      // Handle validation errors
      if (error.name === 'ValidationError') {
        const messages = Object.values(error.errors).map((val: any) => val.message);
        throw new BadRequestException(messages.join(', '));
      }

      // Handle duplicate key error
      if (error.code === 11000) {
        throw new ConflictException('User with this email already exists');
      }

      throw new InternalServerErrorException('Failed to create user');
    }
  }

  async findByEmail(email: string): Promise<IUser | null> {
    try {
      const user = await this.userModel
        .findOne({ email: email.toLowerCase() })
        .select('-password')
        .exec();

      return user;
    } catch (error) {
      throw new InternalServerErrorException('Failed to find user by email');
    }
  }

  async findByEmailWithPassword(email: string): Promise<IUser | null> {
    try {
      const user = await this.userModel
        .findOne({ email: email.toLowerCase() })
        .select('+password')
        .exec();

      return user;
    } catch (error) {
      throw new InternalServerErrorException('Failed to find user by email');
    }
  }

  async findById(id: string): Promise<IUserResponse | null> {
    try {
      const user = await this.userModel
        .findById(id)
        .select('-password')
        .exec();

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle invalid ID format
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid user ID format');
      }

      throw new InternalServerErrorException('Failed to find user');
    }
  }

  async findAll(query: IUserQuery): Promise<IUserPaginationResponse> {
    try {
      const { page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = 'desc' } = query;
      const skip = (page - 1) * limit;

      // Build search query
      let searchQuery = {};
      if (search) {
        searchQuery = {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } },
          ],
        };
      }

      // Build sort object
      const sortObject: any = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };

      // Execute query with pagination
      const [users, total] = await Promise.all([
        this.userModel
          .find(searchQuery)
          .select('-password')
          .skip(skip)
          .limit(limit)
          .sort(sortObject)
          .exec(),
        this.userModel.countDocuments(searchQuery).exec(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        users: users as IUserResponse[],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch users');
    }
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<IUserResponse> {
    try {
      // Check if user exists
      const existingUser = await this.userModel.findById(id);
      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // Check if email is being updated and if it's already taken
      if (updateUserDto.email && updateUserDto.email.toLowerCase() !== existingUser.email) {
        const emailExists = await this.userModel.findOne({
          email: updateUserDto.email.toLowerCase(),
          _id: { $ne: id }
        });

        if (emailExists) {
          throw new ConflictException('Email is already taken by another user');
        }
      }

      // Prepare update data
      const updateData = {
        ...updateUserDto,
        ...(updateUserDto.email && { email: updateUserDto.email.toLowerCase() }),
      };

      // Update user
      const updatedUser = await this.userModel
        .findByIdAndUpdate(id, updateData, {
          new: true,
          runValidators: true
        })
        .select('-password')
        .exec();

      if (!updatedUser) {
        throw new NotFoundException('User not found');
      }

      return updatedUser as IUserResponse;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle validation errors
      if (error.name === 'ValidationError') {
        const messages = Object.values(error.errors).map((val: any) => val.message);
        throw new BadRequestException(messages.join(', '));
      }

      // Handle invalid ID format
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid user ID format');
      }

      throw new InternalServerErrorException('Failed to update user');
    }
  }

  async deleteUser(id: string): Promise<IUserResponse> {
    try {
      const deletedUser = await this.userModel
        .findByIdAndDelete(id)
        .select('-password')
        .exec();

      if (!deletedUser) {
        throw new NotFoundException('User not found');
      }

      return deletedUser as IUserResponse;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle invalid ID format
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid user ID format');
      }

      throw new InternalServerErrorException('Failed to delete user');
    }
  }

  async updatePassword(id: string, newPassword: string): Promise<void> {
    try {
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      const updatedUser = await this.userModel
        .findByIdAndUpdate(id, { password: hashedPassword }, { new: true })
        .exec();

      if (!updatedUser) {
        throw new NotFoundException('User not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle invalid ID format
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid user ID format');
      }

      throw new InternalServerErrorException('Failed to update password');
    }
  }

  async updateResetToken(email: string, token: string, otp: string, expires: Date): Promise<void> {
    try {
      const updatedUser = await this.userModel
        .findOneAndUpdate(
          { email: email.toLowerCase() },
          {
            resetPasswordToken: token,
            resetPasswordOTP: otp,
            resetPasswordExpires: expires
          },
          { new: true }
        )
        .exec();

      if (!updatedUser) {
        throw new NotFoundException('User not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to update reset token');
    }
  }

  async clearResetToken(email: string): Promise<void> {
    try {
      const updatedUser = await this.userModel
        .findOneAndUpdate(
          { email: email.toLowerCase() },
          {
            resetPasswordToken: null,
            resetPasswordOTP: null,
            resetPasswordExpires: null
          },
          { new: true }
        )
        .exec();

      if (!updatedUser) {
        throw new NotFoundException('User not found');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to clear reset token');
    }
  }
}