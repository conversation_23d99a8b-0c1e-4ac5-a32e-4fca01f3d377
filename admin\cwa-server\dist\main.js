"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const cors = require("cors");
const gridfs_service_1 = require("./gridfs/gridfs.service");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, { bodyParser: true });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    app.use(cors({
        origin: '*',
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization'],
    }));
    const gridfsService = app.get(gridfs_service_1.GridFSService);
    app.use((req, res, next) => {
        const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i;
        if (req.method === 'GET' && imageExtensions.test(req.path)) {
            const filename = req.path.substring(1);
            console.log('Image request for:', filename);
            if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
                return next();
            }
            gridfsService.findFileByFilename(filename)
                .then(file => {
                if (!file) {
                    console.log('File not found:', filename);
                    return next();
                }
                console.log('File found:', file.filename);
                res.set({
                    'Content-Type': file.metadata?.mimetype || 'image/jpeg',
                    'Cache-Control': 'public, max-age=3600',
                    'X-Content-Type-Options': 'nosniff',
                    'X-Frame-Options': 'DENY'
                });
                const readStream = gridfsService.createReadStreamByFilename(filename);
                readStream.on('error', (error) => {
                    console.error('GridFS read stream error:', error);
                    if (!res.headersSent) {
                        return next();
                    }
                });
                readStream.pipe(res);
            })
                .catch(error => {
                console.error('Error serving image:', error);
                next();
            });
        }
        else {
            next();
        }
    });
    app.setGlobalPrefix('api');
    await app.listen(4000);
    console.log(`Application is running on: ${await app.getUrl()}`);
    console.log(`Swagger documentation available at: ${await app.getUrl()}/api/docs`);
}
bootstrap();
//# sourceMappingURL=main.js.map